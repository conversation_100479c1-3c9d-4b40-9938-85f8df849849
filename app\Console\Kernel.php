<?php

namespace App\Console;

use App\Models\Feedback;
use App\Models\User;
use App\Notifications\NewFeedbackNotification;
use App\Notifications\SalesReportReminderNotification;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CleanupDuplicateSales::class,
        // ... existing commands ...
    ];

    /**
     * Define the application's command schedule.
     *
     * These schedules are used to run the console commands.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Check for unfeatured feedback every day at 9 AM
        $schedule->call(function () {
            $unfeaturedFeedback = Feedback::where('is_featured', false)->get();
            
            if ($unfeaturedFeedback->isNotEmpty()) {
                $latestFeedback = $unfeaturedFeedback->sortByDesc('created_at')->first();
                $count = $unfeaturedFeedback->count();
                
                // Get all users
                $users = User::all();
                
                foreach ($users as $user) {
                    // Create notification data
                    $notificationData = [
                        'id' => $latestFeedback->id,
                        'name' => $latestFeedback->name,
                        'rating' => $latestFeedback->rating,
                        'message' => $latestFeedback->message,
                        'created_at' => $latestFeedback->created_at->format('Y-m-d H:i:s'),
                        'count' => $count,
                        'type' => 'unfeatured_feedback'
                    ];
                    
                    // Create the notification
                    $user->notifications()->create([
                        'id' => Str::uuid()->toString(),
                        'type' => 'App\Notifications\NewFeedbackNotification',
                        'data' => json_encode($notificationData),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        })->dailyAt('9:00');
        
        // Schedule the unfeatured feedback command to run daily at 9 AM
        // $schedule->command('check:unfeatured-feedback')->dailyAt('09:00');
        
        // Send weekly sales report reminder every Saturday at 9 AM
        $schedule->call(function () {
            // Check if today is Saturday
            if (Carbon::now()->dayOfWeek === Carbon::SATURDAY) {
                $users = User::all();
                
                foreach ($users as $user) {
                    // Create notification data for weekly report
                    $notificationData = [
                        'message' => 'Weekly sales report is now available. Please review your weekly sales data.',
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'type' => 'sales_report_reminder',
                        'period' => 'weekly',
                        'report_url' => url('/reports/generate?period=weekly&user_id=' . $user->id),
                        'user_name' => $user->name,
                        'for_user_id' => $user->id
                    ];
                    
                    // Create the notification
                    $user->notifications()->create([
                        'id' => Str::uuid()->toString(),
                        'type' => 'App\Notifications\SalesReportReminderNotification',
                        'data' => json_encode($notificationData),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        })->saturdays()->at('9:00');
        
        // Send monthly sales report reminder on the 1st of each month at 9 AM
        $schedule->call(function () {
            // Check if today is the first day of the month
            if (Carbon::now()->day === 1) {
                $users = User::all();
                
                foreach ($users as $user) {
                    // Create notification data for monthly report
                    $notificationData = [
                        'message' => 'Monthly sales report for ' . Carbon::now()->subMonth()->format('F Y') . ' is now available.',
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'type' => 'sales_report_reminder',
                        'period' => 'monthly',
                        'report_url' => url('/reports/generate?period=monthly&user_id=' . $user->id),
                        'user_name' => $user->name,
                        'for_user_id' => $user->id
                    ];
                    
                    // Create the notification
                    $user->notifications()->create([
                        'id' => Str::uuid()->toString(),
                        'type' => 'App\Notifications\SalesReportReminderNotification',
                        'data' => json_encode($notificationData),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        })->monthlyOn(1, '9:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
} 