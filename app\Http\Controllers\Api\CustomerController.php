<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CustomerController extends Controller
{
    /**
     * Get all devices for a specific customer
     * 
     * @param int $customerId
     * @return \Illuminate\Http\JsonResponse
     */
    public function devices($customerId)
    {
        try {
            // Find the customer or return 404 response
            $customer = Customer::findOrFail($customerId);
            
            // Security check - ensure the customer belongs to the authenticated user
            if ($customer->user_id !== Auth::id()) {
                return response()->json([
                    'error' => 'Unauthorized access',
                    'message' => 'You do not have permission to view these devices'
                ], 403);
            }
            
            // Get all devices for this customer
            $devices = $customer->devices()
                ->with(['deviceModel', 'repairs' => function($query) {
                    $query->latest('created_at');
                }])
                ->get();
            
            // Format device data
            $formattedDevices = $devices->map(function ($device) {
                // Get the latest repair
                $latestRepair = $device->repairs->first();
                
                return [
                    'id' => $device->id,
                    'brand' => $device->brand ?? $device->deviceModel->brand ?? '',
                    'model' => $device->model ?? $device->deviceModel->model_name ?? '',
                    'serial_number' => $device->serial_number ?? '',
                    'status' => $device->status,
                    'created_at' => $device->created_at,
                    'latest_repair' => $latestRepair ? [
                        'id' => $latestRepair->id,
                        'status' => $latestRepair->status,
                        'created_at' => $latestRepair->created_at
                    ] : null
                ];
            });
            
            return response()->json($formattedDevices);
        } catch (\Exception $e) {
            Log::error('Error in API devices method', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'Error loading devices',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
