<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Controllers\UserController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

class AdminVerificationController extends Controller
{
    /**
     * Display the admin verification page.
     *
     * @return \Illuminate\View\View
     */
    public function show()
    {
        return view('auth.verify-admin');
    }

    /**
     * Verify the admin password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify(Request $request)
    {
        $request->validate([
            'admin_password' => 'required|string',
        ]);

        // Get the current admin password hash from database
        $passwordHash = UserController::getAdminPasswordHash();

        // Check if the password matches using Hash::check
        if (!Hash::check($request->admin_password, $passwordHash)) {
            throw ValidationException::withMessages([
                'admin_password' => ['The provided password is incorrect.'],
            ]);
        }

        // Mark admin as verified in the session
        session(['admin_verified' => true]);

        // If password is correct, redirect to register page
        return redirect()->route('register');
    }
} 