<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Repair;
use App\Models\Customer;
use App\Models\Service;
use Carbon\Carbon;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;

class DashboardController extends Controller
{
    public function index(): View
    {
        try {
            // Get the current user
            $user = Auth::user();
            
            // Get the current date
            $now = Carbon::now();

            // Get selected periods from request
            $selectedWeek = (int) request('week', 0);
            $selectedMonth = request('month', $now->format('Y-m'));
            $selectedYear = (int) request('year', $now->year);

            // Parse selected month
            $selectedMonthDate = Carbon::createFromFormat('Y-m', $selectedMonth);

            // Get pending repairs with error handling - filtered by user
            try {
                $pendingRepairs = Repair::where('status', 'pending')
                    ->where('user_id', $user->id)
                    ->count();
            } catch (Exception $e) {
                $pendingRepairs = 0;
            }

            // Get total customers with error handling - filtered by user
            try {
                $totalCustomers = Customer::where('user_id', $user->id)->count();
            } catch (Exception $e) {
                $totalCustomers = 0;
            }

            // Get active services with error handling - filtered by user
            try {
                $activeServices = Service::where('is_active', true)
                    ->where('user_id', $user->id)
                    ->count();
            } catch (Exception $e) {
                $activeServices = 0;
            }

            // Get unpaid repairs count with error handling - filtered by user
            try {
                $unpaidRepairsCount = Repair::where('payment_status', 'unpaid')
                    ->where('user_id', $user->id)
                    ->count();
            } catch (Exception $e) {
                $unpaidRepairsCount = 0;
            }

            // Get low stock items with error handling
            try {
                $lowStockItems = DB::table('inventory')
                    ->where('quantity', '<', DB::raw('reorder_level'))
                    ->count();
            } catch (Exception $e) {
                $lowStockItems = 0;
            }

            // Weekly sales (selected week) - filtered by user's repairs
            try {
                $weekStart = $now->copy()->subWeeks($selectedWeek)->startOfWeek();
                $weekEnd = $weekStart->copy()->endOfWeek();
                $weeklySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                    ->where('repairs.user_id', $user->id)
                    ->whereBetween('sale_date', [$weekStart, $weekEnd])
                    ->sum('sales.amount');
            } catch (Exception $e) {
                $weeklySales = 0;
            }

            // Monthly sales (selected month) - filtered by user's repairs
            try {
                $monthlySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                    ->where('repairs.user_id', $user->id)
                    ->whereYear('sale_date', $selectedMonthDate->year)
                    ->whereMonth('sale_date', $selectedMonthDate->month)
                    ->sum('sales.amount');
            } catch (Exception $e) {
                $monthlySales = 0;
            }

            // Yearly sales (selected year) - filtered by user's repairs
            try {
                $yearlySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                    ->where('repairs.user_id', $user->id)
                    ->whereYear('sale_date', $selectedYear)
                    ->sum('sales.amount');
            } catch (Exception $e) {
                $yearlySales = 0;
            }

            // Daily sales for the chart (last 7 days) - filtered by user's repairs
            try {
                $dailySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                    ->where('repairs.user_id', $user->id)
                    ->whereBetween('sale_date', [
                        $now->copy()->subDays(6)->startOfDay(),
                        $now->endOfDay()
                    ])
                    ->selectRaw('DATE(sale_date) as date, SUM(sales.amount) as total')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get()
                    ->pluck('total', 'date')
                    ->toArray();
            } catch (Exception $e) {
                $dailySales = [];
            }

            // Fill in missing days with zero
            $salesData = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = $now->copy()->subDays($i)->format('Y-m-d');
                $salesData[$date] = $dailySales[$date] ?? 0;
            }

            // Return view with all required data
            return view('dashboard', compact(
                'pendingRepairs',
                'totalCustomers',
                'activeServices',
                'lowStockItems',
                'unpaidRepairsCount',
                'weeklySales',
                'monthlySales',
                'yearlySales',
                'salesData',
                'selectedMonth',
                'selectedYear',
                'weekStart',
                'weekEnd'
            ));

        } catch (Exception $e) {
            // Get current date for default week range
            $now = Carbon::now();
            $weekStart = $now->copy()->startOfWeek();
            $weekEnd = $now->copy()->endOfWeek();
            
            // If anything fails, return view with default values
            return view('dashboard', [
                'pendingRepairs' => 0,
                'totalCustomers' => 0,
                'activeServices' => 0,
                'lowStockItems' => 0,
                'unpaidRepairsCount' => 0,
                'weeklySales' => 0,
                'monthlySales' => 0,
                'yearlySales' => 0,
                'salesData' => array_fill_keys(
                    array_map(
                        fn($i) => Carbon::now()->subDays($i)->format('Y-m-d'),
                        range(6, 0)
                    ),
                    0
                ),
                'selectedMonth' => now()->format('F Y'),
                'selectedYear' => now()->year,
                'weekStart' => $weekStart,
                'weekEnd' => $weekEnd
            ]);
        }
    }

    public function getSalesData()
    {
        try {
            $user = Auth::user();
            $now = Carbon::now();
            
            $selectedWeek = (int) request('week', 0);
            $selectedMonth = request('month', $now->format('Y-m'));
            $selectedYear = (int) request('year', $now->year);
            
            $selectedMonthDate = Carbon::createFromFormat('Y-m', $selectedMonth);
            
            // Weekly sales
            $weekStart = $now->copy()->subWeeks($selectedWeek)->startOfWeek();
            $weekEnd = $weekStart->copy()->endOfWeek();
            $weeklySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                ->where('repairs.user_id', $user->id)
                ->whereBetween('sale_date', [$weekStart, $weekEnd])
                ->sum('sales.amount');
            
            // Monthly sales
            $monthlySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                ->where('repairs.user_id', $user->id)
                ->whereYear('sale_date', $selectedMonthDate->year)
                ->whereMonth('sale_date', $selectedMonthDate->month)
                ->sum('sales.amount');
            
            // Yearly sales
            $yearlySales = Sale::join('repairs', 'sales.repair_id', '=', 'repairs.id')
                ->where('repairs.user_id', $user->id)
                ->whereYear('sale_date', $selectedYear)
                ->sum('sales.amount');
            
            return response()->json([
                'weeklySales' => number_format($weeklySales, 2),
                'monthlySales' => number_format($monthlySales, 2),
                'yearlySales' => number_format($yearlySales, 2),
                'weekRange' => $weekStart->format('M d') . ' - ' . $weekEnd->format('M d'),
                'selectedMonth' => $selectedMonthDate->format('F Y'),
                'selectedYear' => $selectedYear,
                'currentSelections' => [
                    'week' => $selectedWeek,
                    'month' => $selectedMonth,
                    'year' => $selectedYear
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch sales data'
            ], 500);
        }
    }
} 