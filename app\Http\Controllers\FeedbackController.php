<?php

namespace App\Http\Controllers;

use App\Models\Feedback;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class FeedbackController extends Controller
{
    public function index(Request $request)
    {
        // Show all feedback to all users
        $feedbacks = Feedback::latest()->paginate(10);
        
        return view('feedback.index', compact('feedbacks'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'message' => 'required|string',
            'rating' => 'required|integer|min:1|max:5',
            'service_type' => 'nullable|string|max:255',
        ]);

        // Set user_id only if the user is authenticated
        if (Auth::check()) {
            $validated['user_id'] = Auth::id();
        }

        $feedback = Feedback::create($validated);

        // Check if this is an AJAX request
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true, 
                'message' => 'Thank you for your feedback!',
                'feedback_id' => $feedback->id
            ]);
        }

        // Regular form submission
        return redirect()->back()->with('success', 'Thank you for your feedback!')->withFragment('testimonials');
    }

    public function toggleFeatured(Request $request, Feedback $feedback)
    {
        // Allow any user to toggle featured status
        $feedback->update(['is_featured' => !$feedback->is_featured]);
        return redirect()->back()->with('success', 'Feedback status updated successfully.');
    }

    public function destroy(Request $request, Feedback $feedback)
    {
        // Allow any user to delete feedback
        $feedback->delete();
        return redirect()->back()->with('success', 'Feedback deleted successfully.');
    }
    
    public function share(Request $request, Feedback $feedback)
    {
        // Allow any authenticated user to view any feedback
        if (!$request->user()) {
            abort(403, 'Please login to view feedback details.');
        }
        
        return view('feedback.share', compact('feedback'));
    }
}
