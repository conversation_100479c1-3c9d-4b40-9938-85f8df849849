<?php

namespace App\Http\Controllers;

use App\Models\Feedback;
use App\Models\Repair;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Notifications\DatabaseNotification;

class NotificationController extends Controller
{
    /**
     * Show the notifications page
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get notifications
        $notifications = DB::table('notifications')
            ->where('notifiable_id', $user->id)
            ->where('notifiable_type', get_class($user))
            ->latest('created_at')
            ->take(10)
            ->get();
        
        // Get pending repairs for the current user
        $pendingRepairs = Repair::with(['items.device.customer', 'items.service'])
            ->where('user_id', $user->id)
            ->where('status', 'pending')
            ->latest()
            ->take(5)
            ->get();
            
        // Get in-progress repairs for the current user
        $inProgressRepairs = Repair::with(['items.device.customer', 'items.service'])
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->latest()
            ->take(5)
            ->get();
        
        // Get unpaid repairs for the current user
        $unpaidRepairs = Repair::with(['items.device.customer', 'items.service'])
            ->where('user_id', $user->id)
            ->where('payment_status', 'unpaid')
            ->where('status', '!=', 'cancelled')
            ->where(function($query) {
                $query->where('status', 'completed')
                      ->orWhere('status', 'ready_for_pickup');
            })
            ->latest()
            ->take(5)
            ->get();
        
        // Calculate counts
        $unreadNotificationsCount = $user->unreadNotifications->count();
        $pendingRepairsCount = $pendingRepairs->count();
        $inProgressRepairsCount = $inProgressRepairs->count();
        $unpaidRepairsCount = $unpaidRepairs->count();
        $totalCount = $unreadNotificationsCount + $pendingRepairsCount + $inProgressRepairsCount + $unpaidRepairsCount;
        
        return view('notifications.index', [
            'notifications' => $notifications,
            'pendingRepairs' => $pendingRepairs,
            'inProgressRepairs' => $inProgressRepairs,
            'unpaidRepairs' => $unpaidRepairs,
            'unreadCount' => $totalCount
        ]);
    }
    
    /**
     * Get the notification counts for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNotificationCounts()
    {
        $user = Auth::user();
        
        // Get the unread notification count
        $unreadCount = DatabaseNotification::where('notifiable_id', $user->id)
            ->where('notifiable_type', get_class($user))
            ->whereNull('read_at')
            ->where(function($query) {
                // Exclude feedback notifications
                $query->where('type', 'NOT LIKE', '%FeedbackNotification%');
            })
            ->count();
        
        // Get the pending repairs count for the current user
        $pendingRepairsCount = Repair::where('status', 'pending')
            ->where('user_id', $user->id)
            ->count();
        
        // Get the in-progress repairs count for the current user
        $inProgressRepairsCount = Repair::where('status', 'in_progress')
            ->where('user_id', $user->id)
            ->count();
        
        // Get only unpaid repairs count, regardless of status
        $unpaidRepairsCount = Repair::where('payment_status', 'unpaid')
            ->where('status', '!=', 'cancelled')
            ->where('user_id', $user->id)
            ->count();
        
        // Calculate total count
        $totalCount = $unreadCount + $pendingRepairsCount + $inProgressRepairsCount + $unpaidRepairsCount;
        
        return response()->json([
            'unread_count' => $unreadCount,
            'pending_repairs_count' => $pendingRepairsCount,
            'in_progress_repairs_count' => $inProgressRepairsCount,
            'unpaid_repairs_count' => $unpaidRepairsCount,
            'total_count' => $totalCount
        ]);
    }
    
    /**
     * Get the notifications and counts for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNotifications()
    {
        try {
            $user = Auth::user();
            
            // Log the request for debugging
            Log::info('Getting notifications for user', [
                'user_id' => $user->id,
                'user_email' => $user->email
            ]);
            
            // Get the user's notifications
            $notifications = DatabaseNotification::where('notifiable_id', $user->id)
                ->where('notifiable_type', get_class($user))
                ->orderBy('created_at', 'desc')
                ->get()
                ->filter(function ($notification) {
                    // Filter out feedback notifications
                    return !str_contains($notification->type, 'FeedbackNotification');
                })
                ->values();
            
            // Get the pending repairs for the current user
            $pendingRepairs = Repair::where('status', 'pending')
                ->where('user_id', $user->id)
                ->with(['items.device.customer', 'items.service'])
                ->orderBy('created_at', 'desc')
                ->get();
            
            // Get the in-progress repairs for the current user
            $inProgressRepairs = Repair::where('status', 'in_progress')
                ->where('user_id', $user->id)
                ->with(['items.device.customer', 'items.service'])
                ->orderBy('created_at', 'desc')
                ->get();
            
            // Get only the unpaid repairs for the current user
            $unpaidRepairs = Repair::where('payment_status', 'unpaid')
                ->where('status', '!=', 'cancelled')
                ->where('user_id', $user->id)
                ->with(['items.device.customer', 'items.service'])
                ->orderBy('created_at', 'desc')
                ->get();
            
            // Make sure the data is serializable
            $processedNotifications = $notifications->map(function($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'notifiable_id' => $notification->notifiable_id,
                    'notifiable_type' => $notification->notifiable_type,
                    'data' => json_decode($notification->data),
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at->toDateTimeString(),
                    'updated_at' => $notification->updated_at->toDateTimeString()
                ];
            });
            
            $response = [
                'notifications' => $processedNotifications,
                'pending_repairs' => $pendingRepairs,
                'in_progress_repairs' => $inProgressRepairs,
                'unpaid_repairs' => $unpaidRepairs
            ];
            
            Log::info('Notifications data retrieved', [
                'notification_count' => count($processedNotifications),
                'pending_repairs_count' => count($pendingRepairs),
                'in_progress_repairs_count' => count($inProgressRepairs),
                'unpaid_repairs_count' => count($unpaidRepairs)
            ]);
            
            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('Error getting notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'Failed to retrieve notifications',
                'message' => $e->getMessage(),
                'notifications' => [],
                'pending_repairs' => [],
                'in_progress_repairs' => [],
                'unpaid_repairs' => []
            ], 500);
        }
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        $user = Auth::user();
        $user->unreadNotifications->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read',
        ]);
    }
    
    /**
     * Mark a specific notification as read
     */
    public function markAsRead(Request $request)
    {
        // Support both JSON and form data
        $notificationId = $request->input('notification_id');
        $user = Auth::user();
        
        // Log request data for debugging
        Log::info('Mark as read request', [
            'notification_id' => $notificationId,
            'user_id' => $user->id,
            'is_json' => $request->isJson(),
            'content_type' => $request->header('Content-Type'),
            'all_input' => $request->all()
        ]);
        
        // Find and mark notification as read
        $notification = DB::table('notifications')
            ->where('id', $notificationId)
            ->where('notifiable_id', $user->id)
            ->where('notifiable_type', get_class($user))
            ->first();
        
        if ($notification) {
            DB::table('notifications')
                ->where('id', $notificationId)
                ->update(['read_at' => now()]);
                
            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read',
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Notification not found',
        ], 404);
    }
} 