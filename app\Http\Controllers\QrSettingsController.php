<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\QrSetting;

class QrSettingsController extends Controller
{
    /**
     * Display QR settings page
     */
    public function index()
    {
        $gcashEnabled = QrSetting::isGcashEnabled();
        $paymayaEnabled = QrSetting::isPaymayaEnabled();

        return view('qr-settings.index', compact('gcashEnabled', 'paymayaEnabled'));
    }

    /**
     * Toggle QR payment method
     */
    public function toggle(Request $request)
    {
        try {
            $validated = $request->validate([
                'method' => 'required|in:gcash,paymaya',
                'enabled' => 'required|boolean'
            ]);

            Log::info('QR Toggle Request', [
                'method' => $validated['method'],
                'enabled' => $validated['enabled']
            ]);

            $result = QrSetting::toggleMethod($validated['method'], $validated['enabled']);

            Log::info('QR Toggle Result', ['result' => $result]);

            return response()->json([
                'success' => true,
                'message' => ucfirst($validated['method']) . ' QR code ' . ($validated['enabled'] ? 'enabled' : 'disabled') . ' successfully'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('QR Toggle Validation Error', ['errors' => $e->errors()]);
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('QR Toggle Error', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current QR settings for API
     */
    public function getSettings()
    {
        return response()->json([
            'gcash_enabled' => QrSetting::isGcashEnabled(),
            'paymaya_enabled' => QrSetting::isPaymayaEnabled(),
            'enabled_methods' => QrSetting::getEnabledMethods()
        ]);
    }
}
