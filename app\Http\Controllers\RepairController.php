<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Device;
use App\Models\Repair;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Redirect;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class RepairController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = \Illuminate\Support\Facades\Auth::user();
        $query = Repair::with(['items.device.customer', 'items.service']);
        
        // Always filter repairs by user - no admin exception
        $query->where('user_id', $user->id);
        
        // Handle filter parameter for payment status
        if ($request->has('filter')) {
            $filter = $request->input('filter');
            if ($filter === 'unpaid') {
                $query->where('payment_status', 'unpaid');
                session()->flash('info', "Viewing unpaid repairs only");
            }
        }
        
        // Handle customer_id and device_id parameters coming from device links
        $customerId = null;
        $deviceId = null;
        
        if ($request->has('customer_id') && $request->has('device_id')) {
            $customerId = $request->input('customer_id');
            $deviceId = $request->input('device_id');
            
            // Get customer and device information to display in the info message
            $customer = Customer::find($customerId);
            $device = Device::find($deviceId);
            
            if ($customer && $device) {
                // Pass info message
                session()->flash('info', "Viewing repairs for {$customer->name}'s {$device->brand} {$device->model}");
                
                // Filter repairs to show only those for this customer and device
                $query->whereHas('items', function($q) use ($deviceId) {
                    $q->where('device_id', $deviceId);
                });
            }
        }

        // Handle search
        if ($request->has('search')) {
            $searchTerm = $request->search;
            $query->where(function($query) use ($searchTerm) {
                $query->whereHas('items.device.customer', function($q) use ($searchTerm) {
                    $q->where('name', 'like', "%{$searchTerm}%");
                })
                ->orWhereHas('items.device', function($q) use ($searchTerm) {
                    $q->where('brand', 'like', "%{$searchTerm}%")
                      ->orWhere('model', 'like', "%{$searchTerm}%");
                })
                ->orWhereHas('items.service', function($q) use ($searchTerm) {
                    $q->where('name', 'like', "%{$searchTerm}%");
                })
                ->orWhere('status', 'like', "%{$searchTerm}%");
            });
        }

        // Handle sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');

        // Define allowed sort fields
        $allowedSortFields = [
            'customer' => 'items.device.customer.name',
            'device' => 'items.device.brand',
            'service' => 'items.service.name',
            'status' => 'status',
            'created_at' => 'created_at'
        ];

        if (array_key_exists($sortField, $allowedSortFields)) {
            if (str_contains($allowedSortFields[$sortField], '.')) {
                // Handle relationship sorting through repair items
                $query->join('repair_items', 'repairs.id', '=', 'repair_items.repair_id')
                      ->join('devices', 'repair_items.device_id', '=', 'devices.id')
                      ->join('customers', 'devices.customer_id', '=', 'customers.id')
                      ->join('services', 'repair_items.service_id', '=', 'services.id')
                      ->orderBy($allowedSortFields[$sortField], $sortDirection)
                      ->select('repairs.*')
                      ->distinct();
            } else {
                $query->orderBy($allowedSortFields[$sortField], $sortDirection);
            }
        }

        $perPage = $request->get('perPage', 10);
        $repairs = $query->paginate($perPage)->withQueryString();
        
        // Only get customers that belong to the current user
        $customers = Customer::where('user_id', $user->id)->orderBy('name')->get();
        
        // Only get services that belong to the current user
        $services = Service::active()
            ->where('user_id', Auth::id())
            ->with('category')
            ->orderBy('name')
            ->get();
        
        return View::make('repairs.index', compact(
            'repairs', 
            'sortField', 
            'sortDirection', 
            'customers', 
            'services',
            'customerId',
            'deviceId',
            'request'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Redirect to repairs index page since the create view has been removed
        return redirect()->route('repairs.index')->with('info', 'Repairs can only be created from the device list in the Customer Profile');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Create the repair with status and user_id
        $repair = Repair::create([
            'status' => 'pending', // Set initial status
            'notes' => $request->notes,
            'started_at' => now()->timezone('Asia/Manila'), // Set started_at to current time in Manila timezone
            'payment_method' => $request->payment_method,
            'user_id' => \Illuminate\Support\Facades\Auth::id(),
        ]);
        
        // Validate the basic repair data
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.device_id' => 'required|exists:devices,id',
            'items.*.service_id' => 'required|exists:services,id',
            'items.*.cost' => 'required|numeric|min:0',
            'items.*.status' => 'required|in:pending,in_progress,completed,cancelled',
            'items.*.notes' => 'nullable|string|max:500',
        ]);
        
        // Create repair items
        foreach ($request->items as $itemData) {
            $repair->items()->create([
                'device_id' => $itemData['device_id'],
                'service_id' => $itemData['service_id'],
                'cost' => $itemData['cost'],
                'notes' => $itemData['notes'] ?? null,
            ]);
        }
        
        // If the request is AJAX, return JSON
        if ($request->ajax() || $request->wantsJson()) {
            // Load the repair with its relationships for the JSON response
            $repair->load(['items.device.customer', 'items.service']);
            
            return response()->json([
                'success' => true,
                'message' => 'Repair created successfully!',
                'repair' => $repair
            ]);
        }
        
        // Normal redirect response
        return redirect()->route('repairs.index')
            ->with('success', 'Repair created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Repair $repair)
    {
        // Check if repair belongs to the current user
        if ($repair->user_id !== Auth::id()) {
            return abort(403, 'Unauthorized action. This repair does not belong to you.');
        }
        
        $repair->load(['items.device.customer', 'items.service']);
        return View::make('repairs.show', compact('repair'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Repair $repair)
    {
        // Check if repair belongs to the current user
        if ($repair->user_id !== Auth::id()) {
            return abort(403, 'Unauthorized action. This repair does not belong to you.');
        }
        
        $repair->load(['items.device.customer', 'items.service']);
        
        // Get the customer ID from the first repair item
        $customerId = $repair->items->first()->device->customer_id ?? null;
        
        // Only get devices that belong to this specific customer
        $devices = $customerId ? 
            Device::where('customer_id', $customerId)->with('customer')->get() : 
            collect();
        
        // Only get services that belong to the current user
        $services = Service::active()
            ->where('user_id', Auth::id())
            ->with('category')
            ->orderBy('name')
            ->get();
        
        // Calculate duration_seconds if not already set
        if (!isset($repair->duration_seconds) && $repair->started_at) {
            $endTime = $repair->completed_at ?? now();
            $repair->duration_seconds = $endTime->diffInSeconds($repair->started_at);
        } else if (!isset($repair->duration_seconds)) {
            $repair->duration_seconds = 0;
        }
        
        return View::make('repairs.edit', compact('repair', 'devices', 'services'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Repair $repair)
    {
        $validated = $request->validate([
            'items' => ['required', 'array', 'min:1'],
            'items.*.device_id' => ['required', 'exists:devices,id'],
            'items.*.service_id' => ['required', 'exists:services,id'],
            'items.*.cost' => ['required', 'numeric', 'min:0'],
            'items.*.notes' => ['nullable', 'string'],
            'status' => ['required', 'in:pending,in_progress,completed,cancelled'],
            'notes' => ['nullable', 'string'],
            'started_at' => ['nullable', 'date'],
            'completed_at' => ['nullable', 'date', 'after_or_equal:started_at'],
            'payment_method' => ['required', 'in:cash,gcash/maya,bank_transfer,credit_card'],
            'duration_seconds' => ['nullable', 'integer', 'min:0'],
        ]);

        $oldStatus = $repair->status;
        $newStatus = $validated['status'];

        // Prepare update data
        $updateData = [
            'status' => $newStatus,
            'notes' => $validated['notes'],
            'payment_method' => $validated['payment_method'],
        ];

        // Calculate and track accumulated duration for timer
        $accumulatedSeconds = 0;
        if (isset($request->duration_seconds)) {
            $accumulatedSeconds = (int)$request->duration_seconds;
        } else if ($repair->started_at && $oldStatus === 'in_progress') {
            // If we were already in progress, calculate elapsed time up to now
            $accumulatedSeconds = now()->diffInSeconds($repair->started_at);
        }
        
        // Handle status transitions and timestamps
        if ($newStatus === 'in_progress') {
            if ($oldStatus !== 'in_progress') {
                // Starting a new repair timer
                $updateData['started_at'] = now()->timezone('Asia/Manila');
                $updateData['duration_seconds'] = $accumulatedSeconds;
                $updateData['completed_at'] = null;
            } else {
                // Continuing an existing timer - keep the start time, accumulate seconds
                $updateData['duration_seconds'] = $accumulatedSeconds;
            }
        }
        elseif ($newStatus === 'completed') {
            // Ensure we have a started_at time
            if (!$repair->started_at) {
                $updateData['started_at'] = now()->timezone('Asia/Manila');
            }
            $updateData['completed_at'] = now()->timezone('Asia/Manila');
            
            // Calculate total duration when completing
            if ($repair->started_at) {
                $elapsed = now()->diffInSeconds($repair->started_at);
                $updateData['duration_seconds'] = $accumulatedSeconds > 0 ? 
                    $accumulatedSeconds : $elapsed;
            } else {
                $updateData['duration_seconds'] = $accumulatedSeconds;
            }
        }
        elseif ($newStatus === 'pending' || $newStatus === 'cancelled') {
            $updateData['completed_at'] = null;
            // Reset duration if explicitly going back to pending
            if ($newStatus === 'pending') {
                $updateData['duration_seconds'] = 0;
                $updateData['started_at'] = null;
            } else {
                // For cancelled, keep the duration
                $updateData['duration_seconds'] = $accumulatedSeconds;
            }
        }

        // Update the repair
        $repair->update($updateData);

        // Delete existing items and create new ones
        $repair->items()->delete();
        foreach ($validated['items'] as $item) {
            $repair->items()->create([
                'device_id' => $item['device_id'],
                'service_id' => $item['service_id'],
                'cost' => $item['cost'],
                'notes' => $item['notes'] ?? null,
            ]);
        }

        return Redirect::route('repairs.show', $repair)->with('success', 'Repair updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Repair $repair)
    {
        $repair->delete();
        return Redirect::route('repairs.index')->with('success', 'Repair deleted successfully.');
    }

    /**
     * Generate a receipt for a completed repair.
     */
    public function receipt(Repair $repair)
    {
        if ($repair->status !== 'completed') {
            return Redirect::back()->with('error', 'Receipt is only available for completed repairs.');
        }

        $repair->load(['items.device.customer', 'items.service']);
        return View::make('repairs.receipt', compact('repair'));
    }

    /**
     * Toggle the payment status of a repair.
     */
    public function togglePaymentStatus(Repair $repair)
    {
        // Check if repair belongs to the current user
        if ($repair->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized action. This repair does not belong to you.'
            ], 403);
        }

        // Toggle payment status
        $newStatus = $repair->payment_status === 'paid' ? 'unpaid' : 'paid';
        $repair->update(['payment_status' => $newStatus]);

        return response()->json([
            'success' => true,
            'message' => 'Payment status updated successfully',
            'payment_status' => $newStatus
        ]);
    }

    /**
     * Mark a repair as paid with the specified payment method.
     */
    public function markAsPaid(Request $request, Repair $repair)
    {
        // Validate the payment method
        $validated = $request->validate([
            'payment_method' => 'required|in:cash,gcash/maya,bank',
        ]);

        // Update the repair
        $repair->update([
            'payment_status' => 'paid',
            'payment_method' => $validated['payment_method'],
        ]);

        // Redirect back with success message
        return back()->with('success', 'Payment has been recorded successfully.');
    }
} 
