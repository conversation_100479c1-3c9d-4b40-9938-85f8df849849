<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class UserController extends Controller
{
    /**
     * The default admin password for user management access
     */
    public static $defaultAdminPassword = 'admin123';
    
    /**
     * Key for storing the admin password in the database
     */
    private static $adminPasswordKey = 'admin_password';
    
    /**
     * Get the current admin password hash
     */
    public static function getAdminPasswordHash()
    {
        // Get the password hash from the database
        $hash = Setting::get(self::$adminPasswordKey);
        
        // If no hash exists yet, create one from the default password
        if (!$hash) {
            $hash = self::saveAdminPassword(self::$defaultAdminPassword);
        }
        
        return $hash;
    }
    
    /**
     * Save the admin password to the database (hashed with bcrypt)
     */
    public static function saveAdminPassword($password)
    {
        // Hash the password with bcrypt (default algorithm)
        $hash = Hash::make($password);
        
        // Save the hash to the settings
        Setting::set(self::$adminPasswordKey, $hash);
        
        return $hash;
    }
    
    /**
     * Verify access for user management
     */
    public function verifyAccess(Request $request)
    {
        // Always redirect to verification form
        return view('users.verify');
    }
    
    /**
     * Process the verification
     */
    public function processVerification(Request $request)
    {
        $request->validate([
            'password' => ['required', 'string'],
        ]);
        
        // Get the current admin password hash from database
        $passwordHash = self::getAdminPasswordHash();
        
        // Check if the password matches using Hash::check
        if (Hash::check($request->password, $passwordHash)) {
            // Grant access for this request only
            return redirect()->route('users.index')
                ->with('success', 'Access granted to User Management');
        }
        
        return back()->withErrors([
            'password' => 'The provided password is incorrect.',
        ]);
    }
    
    /**
     * Update the admin verification password
     */
    public function updateVerificationPassword(Request $request)
    {
        $request->validate([
            'current_password' => ['required', 'string'],
            'new_password' => ['required', 'string', 'min:6', 'confirmed'],
        ]);
        
        // Get the current admin password hash
        $passwordHash = self::getAdminPasswordHash();
        
        // Check if current password matches using Hash::check
        if (!Hash::check($request->current_password, $passwordHash)) {
            return back()->withErrors([
                'current_password' => 'The current password is incorrect.',
            ]);
        }
        
        // Store the new password hash in database for persistence
        if (self::saveAdminPassword($request->new_password)) {
            return redirect()->route('users.verify')
                ->with('success', 'Verification password updated successfully. Please verify again to continue.');
        }
        
        return back()->withErrors([
            'new_password' => 'Failed to update the password. Please try again.',
        ]);
    }
    
    /**
     * Display a listing of the users.
     */
    public function index(Request $request)
    {
        // Grant access only for the current request coming directly from verification
        if (!$request->session()->has('success')) {
            return redirect()->route('users.verify');
        }
        
        // Get all users with their related counts
        $users = User::withCount([
            'repairs as pending_repairs_count' => function($query) {
                $query->where('status', 'pending');
            },
            'repairs as successful_repairs_count' => function($query) {
                $query->where('status', 'completed');
            },
            'customers as total_customers_count',
            'services as total_services_count'
        ])->get();
        
        $showPasswordForm = true; // To display the password change form
        return view('users.index', compact('users', 'showPasswordForm'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'position' => ['required', 'string', 'in:admin,user'],
            'phone' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:255'],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'position' => $request->position,
            'is_admin' => $request->position === 'admin',
            'phone' => $request->phone,
            'address' => $request->address,
        ]);

        event(new Registered($user));

        return redirect()->route('users.verify')
            ->with('success', 'User created successfully. Please verify again to continue.');
    }

    /**
     * Update the specified user's status.
     */
    public function toggleStatus(User $user)
    {
        // Prevent disabling your own account
        if (Auth::id() === $user->id) {
            return redirect()->route('users.verify')
                ->with('error', 'You cannot disable your own account');
        }
        
        $user->is_active = !$user->is_active;
        $user->save();
        
        $status = $user->is_active ? 'enabled' : 'disabled';
        
        return redirect()->route('users.verify')
            ->with('success', "User has been {$status}. Please verify again to continue.");
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent deleting your own account
        if (Auth::id() === $user->id) {
            return redirect()->route('users.verify')
                ->with('error', 'You cannot delete your own account');
        }
        
        try {
            DB::beginTransaction();
            
            // Delete related services
            $user->services()->delete();
            
            // Delete related repairs
            $user->repairs()->delete();
            
            // Delete related customers
            $user->customers()->delete();
            
            // Finally delete the user
            $user->delete();
            
            DB::commit();
            
            return redirect()->route('users.verify')
                ->with('success', 'User and all their data deleted successfully. Please verify again to continue.');
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->route('users.verify')
                ->with('error', 'Failed to delete user: ' . $e->getMessage() . '. Please verify again to continue.');
        }
    }

    /**
     * Reset the user's password to the default value.
     */
    public function resetPassword(User $user)
    {
        // Default password for reset
        $defaultPassword = 'password';
        
        try {
            // Update the user's password
            $user->password = Hash::make($defaultPassword);
            $user->save();
            
            return redirect()->route('users.verify')
                ->with('success', "Password for {$user->name} has been reset to '{$defaultPassword}'. Please verify again to continue.");
        } catch (\Exception $e) {
            return redirect()->route('users.verify')
                ->with('error', 'Failed to reset password: ' . $e->getMessage() . '. Please verify again to continue.');
        }
    }
    
    /**
     * Update a user's position.
     */
    public function updatePosition(Request $request, User $user)
    {
        // Prevent changing your own position
        if (Auth::id() === $user->id) {
            return redirect()->route('users.verify')
                ->with('error', 'You cannot change your own position');
        }
        
        $request->validate([
            'position' => ['required', 'string', 'in:admin,user'],
        ]);
        
        try {
            $user->position = $request->position;
            $user->is_admin = $request->position === 'admin';
            $user->save();
            
            return redirect()->route('users.verify')
                ->with('success', "Position for {$user->name} has been updated to {$user->position_display}. Please verify again to continue.");
        } catch (\Exception $e) {
            return redirect()->route('users.verify')
                ->with('error', 'Failed to update position: ' . $e->getMessage() . '. Please verify again to continue.');
        }
    }
} 