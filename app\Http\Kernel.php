<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\CheckUserIsActive::class,
        ],

        'api' => [
            // \Laravel\Sanctum\Sanctum::class.':sanctum',
            \Illuminate\Routing\Middleware\ThrottleRequests::class.':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * Register any other middleware.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        //
    ];
} 