<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserIsActive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the user is authenticated and not active
        if (Auth::check() && !Auth::user()->is_active) {
            // Log the user out
            Auth::logout();
            
            // Clear the session
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            // Redirect to login with error message
            return redirect()->route('login')
                ->withErrors(['email' => 'Your account has been disabled. Please contact an administrator.']);
        }
        
        return $next($request);
    }
}
