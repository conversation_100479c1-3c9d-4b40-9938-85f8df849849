<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QrSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_method',
        'is_enabled'
    ];

    protected $casts = [
        'is_enabled' => 'boolean'
    ];

    /**
     * Check if GCash QR is enabled
     */
    public static function isGcashEnabled()
    {
        return self::where('payment_method', 'gcash')
                   ->where('is_enabled', true)
                   ->exists();
    }

    /**
     * Check if PayMaya QR is enabled
     */
    public static function isPaymayaEnabled()
    {
        return self::where('payment_method', 'paymaya')
                   ->where('is_enabled', true)
                   ->exists();
    }

    /**
     * Get enabled payment methods
     */
    public static function getEnabledMethods()
    {
        return self::where('is_enabled', true)->pluck('payment_method')->toArray();
    }

    /**
     * Toggle payment method status
     */
    public static function toggleMethod($method, $enabled)
    {
        return self::updateOrCreate(
            ['payment_method' => $method],
            ['is_enabled' => $enabled]
        );
    }
}
