<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;
use Illuminate\Support\Str;

class Repair extends Model
{
    use HasFactory;

    protected $fillable = [
        'status',
        'notes',
        'started_at',
        'completed_at',
        'payment_method',
        'payment_status',
        'user_id',
        'duration_seconds',
        'special_key',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($repair) {
            // Generate a unique special key if not already set
            if (empty($repair->special_key)) {
                $repair->special_key = static::generateUniqueSpecialKey();
            }
        });

        static::updated(function ($repair) {
            if ($repair->isDirty('status')) {
                $oldStatus = $repair->getOriginal('status');
                $newStatus = $repair->status;

                // Only handle sales records
                if ($newStatus === 'completed' && $oldStatus !== 'completed') {
                    // Create a sale record only if it doesn't exist
                    if (!$repair->sales()->exists()) {
                        $repair->sales()->create([
                            'amount' => $repair->total_cost,
                            'sale_date' => $repair->completed_at ?? now()->timezone('Asia/Manila'),
                        ]);
                    }
                } elseif ($oldStatus === 'completed' && $newStatus !== 'completed') {
                    // Delete sales when moving from completed to another status
                    $repair->sales()->delete();
                }
            }
        });
    }

    /**
     * Generate a unique special key for repair tracking
     * 
     * @return string
     */
    protected static function generateUniqueSpecialKey()
    {
        $key = strtoupper(Str::random(8));
        
        // Make sure the key is unique
        while (static::where('special_key', $key)->exists()) {
            $key = strtoupper(Str::random(8));
        }
        
        return $key;
    }

    public function items(): HasMany
    {
        return $this->hasMany(RepairItem::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the user that owns the repair.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getTotalCostAttribute(): float
    {
        return $this->items->sum('cost');
    }

    // Helper methods to get the first device and service
    public function getDeviceAttribute()
    {
        return $this->items->first()?->device;
    }

    public function getServiceAttribute()
    {
        return $this->items->first()?->service;
    }

    public function getDurationAttribute()
    {
        // If repair is pending or cancelled, no duration should be calculated
        if ($this->status === 'pending' || $this->status === 'cancelled') {
            return $this->status === 'pending' ? 'Not started' : 'Cancelled';
        }
        
        // If we have an explicit duration_seconds value, use that
        if (isset($this->attributes['duration_seconds']) && $this->attributes['duration_seconds'] > 0) {
            $duration = $this->attributes['duration_seconds'];
            
            // Format the duration in a human-readable way
            $days = floor($duration / 86400);
            $hours = floor(($duration % 86400) / 3600);
            $minutes = floor(($duration % 3600) / 60);
            $seconds = $duration % 60;
            
            $parts = [];
            if ($days > 0) $parts[] = $days . ' ' . ($days === 1 ? 'day' : 'days');
            if ($hours > 0) $parts[] = $hours . ' ' . ($hours === 1 ? 'hour' : 'hours');
            if ($minutes > 0) $parts[] = $minutes . ' ' . ($minutes === 1 ? 'minute' : 'minutes');
            if ($seconds > 0) $parts[] = $seconds . ' ' . ($seconds === 1 ? 'second' : 'seconds');
            
            return empty($parts) ? '0 seconds' : implode(', ', $parts);
        }
        
        // Fall back to calculated duration based on timestamps
        if (!$this->started_at) {
            return 'Not started';
        }

        // Make sure to use Philippine time (Asia/Manila)
        $startTime = $this->started_at->copy()->timezone('Asia/Manila');
        $endTime = $this->completed_at 
            ? $this->completed_at->copy()->timezone('Asia/Manila') 
            : now()->timezone('Asia/Manila');
            
        return $startTime->diffForHumans($endTime, ['parts' => 2, 'join' => ' and ', 'short' => true]);
    }
} 