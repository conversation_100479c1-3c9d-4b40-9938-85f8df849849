<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Listen for auth attempts to check if user is active
        Event::listen(
            \Illuminate\Auth\Events\Attempting::class,
            function ($event) {
                $user = \App\Models\User::where('email', $event->credentials['email'])->first();
                
                if ($user && !$user->is_active) {
                    // Cancel the login attempt
                    return false;
                }
            }
        );
        
        // Listen for successful login to double-check if user is active
        Event::listen(
            \Illuminate\Auth\Events\Login::class,
            function ($event) {
                if (!$event->user->is_active) {
                    Auth::logout();
                    
                    // We need to throw an exception to interrupt the login process
                    throw new \Illuminate\Auth\AuthenticationException('This account has been disabled.');
                }
            }
        );
    }
}
