<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qr_settings', function (Blueprint $table) {
            $table->id();
            $table->string('payment_method'); // 'gcash' or 'paymaya'
            $table->boolean('is_enabled')->default(true);
            $table->timestamps();

            $table->unique('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qr_settings');
    }
};
