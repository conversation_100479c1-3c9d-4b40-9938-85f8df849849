<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\QrSetting;

class QrSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $qrMethods = [
            [
                'payment_method' => 'gcash',
                'is_enabled' => true
            ],
            [
                'payment_method' => 'paymaya',
                'is_enabled' => true
            ]
        ];

        foreach ($qrMethods as $method) {
            QrSetting::updateOrCreate(
                ['payment_method' => $method['payment_method']],
                $method
            );
        }
    }
}
