/* Custom CSS Variables */
:root {
    --primary-color: #35424a;
    --secondary-color: #ffffff;
    --accent-color: #e74c3c;
    --text-color: #333333;
    --transition-speed: 0.3s;
}

/* Base Styles */
body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

/* Top Bar */
.top-bar {
    background: var(--primary-color);
    color: var(--secondary-color);
    padding: 0.5rem 0;
    font-size: 0.9rem;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Navigation */
.nav-container {
    background: linear-gradient(to right, #000000, #1a1a1a);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-container img {
    height: 40px;
    width: auto;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-speed);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width var(--transition-speed);
}

.nav-link:hover::after {
    width: 100%;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--secondary-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--primary-color);
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .nav-links.active {
        display: flex;
    }

    .nav-link {
        padding: 0.5rem 0;
    }

    .top-bar .container {
        flex-direction: column;
        text-align: center;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Print Styles */
@media print {
    .nav-container,
    .top-bar {
        display: none;
    }
}

header {
    background: #35424a;
    color: #ffffff;
    padding: 20px 0;
    text-align: center;
}

header h1 {
    margin: 0;
    font-size: 2.5em;
}

nav {
    margin: 20px 0;
}

nav a {
    color: #ffffff;
    text-decoration: none;
    margin: 0 15px;
    font-weight: bold;
}

nav a:hover {
    text-decoration: underline;
}

section {
    padding: 20px;
    margin: 20px;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

footer {
    text-align: center;
    padding: 10px 0;
    background: #35424a;
    color: #ffffff;
    position: relative;
    bottom: 0;
    width: 100%;
}

h2 {
    color: #35424a;
}

p {
    line-height: 1.6;
}