@tailwind base;
@tailwind components;
@tailwind utilities;

/* Table Styling */
.print-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.print-table th,
.print-table td {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
}

/* Column Alignments */
.print-table th {
    background-color: #f8fafc;
    font-weight: 600;
}

/* Text alignment classes */
.print-table td:first-child {
    text-align: left; /* Service/Customer name */
}

.print-table td:nth-child(2) {
    text-align: right; /* Count/Repairs */
}

.print-table td:nth-child(3) {
    text-align: right; /* Total Sales/Spent */
}

.print-table td:nth-child(4) {
    text-align: right; /* Average */
}

.print-table td:nth-child(5) {
    text-align: right; /* Percentage */
}

/* Add some spacing for currency and numbers */
.print-table td:nth-child(3),
.print-table td:nth-child(4) {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
}

/* Canvas Styles */
#c {
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
}

body {
  margin: 0;
  overflow-x: hidden;
}

/* Navigation and Hero Section alignment */
.relative.overflow-hidden.min-h-screen {
  padding-top: 4rem; /* Add padding to account for fixed navigation */
}

/* Fix container alignment */
.container {
  max-width: 1280px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Floating Icon Animation */
.floating-icon {
  position: absolute;
  opacity: 0.7;
  animation: float 15s ease-in-out infinite;
  filter: blur(0.5px);
  transform-origin: center center;
}

.floating-icon svg {
  filter: drop-shadow(0 0 3px currentColor);
  transition: all 0.5s ease;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(5deg);
  }
  50% {
    transform: translateY(0) rotate(0deg);
  }
  75% {
    transform: translateY(15px) rotate(-5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

/* Scroll Animations */
.scroll-fade-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-fade-in {
  opacity: 0;
  transition: opacity 0.8s ease-out;
}

.scroll-fade-right {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-fade-left {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-scale-up {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* Classes for when elements are visible */
.in-view {
  opacity: 1;
  transform: translateY(0) translateX(0) scale(1);
}

/* Staggered animation delays */
.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }
.delay-500 { transition-delay: 0.5s; }
.delay-600 { transition-delay: 0.6s; }
.delay-700 { transition-delay: 0.7s; }
.delay-800 { transition-delay: 0.8s; }
.delay-900 { transition-delay: 0.9s; }
.delay-1000 { transition-delay: 1s; }

/* Animation Delay Classes (for the blob animations) */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Define the blob animation */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}
