import './bootstrap';
import './chat.js';
import './webglBackground.js';
import './scrollAnimations.js';

import Alpine from 'alpinejs';

window.Alpine = Alpine;

Alpine.start();

// Handle smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
  // Get all links with hash (#) except those with onclick already defined
  document.querySelectorAll('a[href^="#"]:not([onclick])').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      
      if (targetId === '#') {
        // Scroll to top for empty hash
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } else {
        // Scroll to target element
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth'
          });
        }
      }
    });
  });
});
