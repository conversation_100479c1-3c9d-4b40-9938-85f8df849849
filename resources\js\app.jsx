import './bootstrap';

import Alpine from 'alpinejs';
import React from 'react';
import { createRoot } from 'react-dom/client';
import Chatbot from './Components/Chatbot';

window.Alpine = Alpine;

Alpine.start();

// Initialize React components
document.addEventListener('DOMContentLoaded', () => {
    // Create chatbot container if it doesn't exist
    let chatbotContainer = document.getElementById('chatbot-container');
    if (!chatbotContainer) {
        chatbotContainer = document.createElement('div');
        chatbotContainer.id = 'chatbot-container';
        document.body.appendChild(chatbotContainer);
    }

    // Render Chatbot
    const root = createRoot(chatbotContainer);
    root.render(<Chatbot />);
});
