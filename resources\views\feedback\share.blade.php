@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto py-10 px-4 sm:px-6 lg:px-8">
    <div id="feedback-card" class="bg-white dark:bg-gray-900 rounded-xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 max-w-3xl mx-auto">
        <!-- Header with status badge -->
        <div class="bg-gradient-to-r from-black to-red-700 px-8 py-6 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-48 h-48 bg-white opacity-5 rounded-full -mt-10 -mr-10"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full -mb-6 -ml-6"></div>
            <div class="flex items-center justify-between relative z-10">
                <h1 class="text-2xl font-bold text-white tracking-tight">Customer Feedback</h1>
                <span class="px-4 py-1.5 rounded-full text-xs font-semibold {{ $feedback->is_featured ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 border border-red-300 dark:border-red-700' : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 border border-gray-300 dark:border-gray-700' }}">
                    {{ $feedback->is_featured ? 'Featured Testimonial' : 'Standard Feedback' }}
                </span>
            </div>
        </div>
        
        <!-- Rating and Date -->
        <div class="bg-gray-50 dark:bg-gray-800 px-8 py-4 flex items-center justify-between border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Rating:</span>
                <div class="flex">
                    @for($i = 1; $i <= 5; $i++)
                        <svg class="h-6 w-6 {{ $i <= $feedback->rating ? 'text-red-500' : 'text-gray-300 dark:text-gray-600' }}" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                    @endfor
                </div>
            </div>
            <div class="text-sm font-medium text-gray-600 dark:text-gray-300">
                Submitted on {{ $feedback->created_at->format('F j, Y \a\t g:i A') }}
            </div>
        </div>
        
        <!-- Main content -->
        <div class="px-8 py-6 bg-white dark:bg-gray-900">
            <!-- Customer info card -->
            <div class="mb-8 bg-gray-50 dark:bg-gray-800 rounded-lg p-5 border border-gray-200 dark:border-gray-700 flex items-start shadow-sm">
                <div class="flex-shrink-0 mr-5">
                    <div class="h-14 w-14 rounded-full bg-gradient-to-br from-black to-red-600 flex items-center justify-center text-white font-bold text-lg shadow-md">
                        {{ strtoupper(substr($feedback->name, 0, 1)) }}
                    </div>
                </div>
                <div class="flex-1">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">{{ $feedback->name }}</h2>
                    <p class="text-sm text-gray-600 dark:text-gray-300">{{ $feedback->email }}</p>
                    @if($feedback->service_type)
                        <div class="mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800">
                            <svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            {{ $feedback->service_type }}
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Message -->
            <div class="mb-6">
                <h3 class="text-md font-semibold text-gray-700 dark:text-gray-200 mb-3 flex items-center">
                    <svg class="mr-2 h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                    Feedback Message:
                </h3>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-inner relative">
                    <div class="absolute right-0 bottom-0 opacity-5 text-red-600 dark:text-red-500 font-bold text-6xl mr-3 mb-1 select-none">
                        "
                    </div>
                    <p class="text-gray-800 dark:text-gray-200 whitespace-pre-line relative z-10 italic">{{ $feedback->message }}</p>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="bg-gray-50 dark:bg-gray-800 px-8 py-4 flex justify-between border-t border-gray-200 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                <svg class="h-4 w-4 mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Feedback ID: {{ $feedback->id }} • VS-SMS
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
                vsmart-tune-up
            </div>
        </div>
    </div>
    
    <!-- Action buttons (outside the image capture area) -->
    <div class="mt-6 flex justify-center space-x-4">
        <a href="{{ route('feedback.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to all feedback
        </a>
        
        <form action="{{ route('feedback.toggle-featured', $feedback) }}" method="POST" class="inline">
            @csrf
            @method('PATCH')
            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-700 hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
                {{ $feedback->is_featured ? 'Unfeature' : 'Feature' }}
            </button>
        </form>
        
        <!-- <button type="button" onclick="saveAsImage()" class="inline-flex items-center px-5 py-2.5 border border-gray-800 rounded-lg shadow-lg text-sm font-medium text-white bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-black transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transform hover:scale-105">
            <svg class="-ml-1 mr-2 h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Download as Image
        </button> -->
    </div>
</div>

<!-- Include html2canvas library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<script>
    function saveAsImage() {
        // Show a loading indicator
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-black bg-opacity-95 text-white px-5 py-3 rounded-lg shadow-xl z-50 flex items-center transition-opacity duration-300';
        toast.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="font-medium">Processing screenshot...</span>
        `;
        document.body.appendChild(toast);
        
        // Select the feedback card to be captured
        const element = document.getElementById('feedback-card');
        
        // Store original styles
        const originalStyles = {
            transform: element.style.transform,
            transition: element.style.transition,
            width: element.style.width,
            margin: element.style.margin,
            padding: element.style.padding
        };

        // Set fixed dimensions for high-quality output with better aspect ratio
        const targetWidth = 1600;
        const targetHeight = 1000;
        
        // Temporarily modify the element for capture
        element.style.width = targetWidth + 'px';
        element.style.margin = '0';
        element.style.transform = 'none';
        element.style.transition = 'none';
        
        // Set optimized options for screenshot-like capture
        const options = {
            width: targetWidth,
            height: targetHeight,
            useCORS: true,
            backgroundColor: document.documentElement.classList.contains('dark') ? '#1a202c' : '#ffffff',
            scale: 1, // Use exact dimensions
            logging: false,
            allowTaint: true,
            imageTimeout: 0,
            onclone: (clonedDoc) => {
                const clonedElement = clonedDoc.getElementById('feedback-card');
                if (clonedElement) {
                    // Enhance card appearance - wider card fills more space
                    clonedElement.style.width = '1400px';
                    clonedElement.style.margin = '0 auto';
                    clonedElement.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
                    clonedElement.style.borderRadius = '12px';
                    clonedElement.style.overflow = 'hidden';
                    clonedElement.style.border = document.documentElement.classList.contains('dark') 
                        ? '1px solid rgba(255, 255, 255, 0.1)' 
                        : '1px solid rgba(0, 0, 0, 0.1)';
                    
                    // Adjust heading sizes
                    const headings = clonedElement.querySelectorAll('h1, h2, h3');
                    headings.forEach(h => {
                        if (h.textContent.includes('Customer Feedback')) {
                            h.style.fontSize = '1.75rem';
                        } else if (h.tagName === 'H2') {
                            h.style.fontSize = '1.25rem';
                        }
                    });

                    // Adjust spacing in the card sections
                    const sections = clonedElement.querySelectorAll('.px-8');
                    sections.forEach(section => {
                        section.style.padding = '1.5rem 2rem';
                    });
                    
                    // Fix text alignment issues
                    const paragraphs = clonedElement.querySelectorAll('p, h1, h2, h3, h4, span');
                    paragraphs.forEach(p => {
                        p.style.textAlign = 'left';
                        p.style.maxWidth = '100%';
                        p.style.overflow = 'hidden';
                    });
                    
                    // Make feedback message more readable
                    const feedbackMessage = clonedElement.querySelector('.whitespace-pre-line');
                    if (feedbackMessage) {
                        feedbackMessage.style.fontSize = '1.05rem';
                        feedbackMessage.style.lineHeight = '1.7';
                        feedbackMessage.style.padding = '1rem';
                    }
                    
                    // Make the rating stars larger
                    const stars = clonedElement.querySelectorAll('svg[viewBox="0 0 20 20"]');
                    stars.forEach(star => {
                        star.style.width = '1.75rem';
                        star.style.height = '1.75rem';
                        star.style.filter = 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))';
                        if (star.classList.contains('text-red-500')) {
                            star.style.color = '#e53e3e';
                        }
                    });
                    
                    // Add elegant padding around the card - reduce padding to make card fill more space
                    const wrapper = clonedDoc.createElement('div');
                    wrapper.style.padding = '30px';
                    wrapper.style.position = 'relative';
                    wrapper.style.width = '100%';
                    wrapper.style.height = '100%';
                    wrapper.style.display = 'flex';
                    wrapper.style.flexDirection = 'column';
                    wrapper.style.justifyContent = 'center';
                    wrapper.style.alignItems = 'center';
                    wrapper.style.background = document.documentElement.classList.contains('dark') 
                        ? 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)' 
                        : 'linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%)';
                    
                    // Add decorative elements
                    const circle1 = clonedDoc.createElement('div');
                    circle1.style.position = 'absolute';
                    circle1.style.top = '40px';
                    circle1.style.left = '40px';
                    circle1.style.width = '120px';
                    circle1.style.height = '120px';
                    circle1.style.borderRadius = '50%';
                    circle1.style.background = 'linear-gradient(45deg, #e53e3e 0%, #f56565 100%)';
                    circle1.style.opacity = '0.15';
                    wrapper.appendChild(circle1);
                    
                    const circle2 = clonedDoc.createElement('div');
                    circle2.style.position = 'absolute';
                    circle2.style.bottom = '60px';
                    circle2.style.right = '100px';
                    circle2.style.width = '180px';
                    circle2.style.height = '180px';
                    circle2.style.borderRadius = '50%';
                    circle2.style.background = 'linear-gradient(45deg, #000000 0%, #1a202c 100%)';
                    circle2.style.opacity = '0.1';
                    wrapper.appendChild(circle2);
                    
                    // Insert wrapper before the card and move card into it
                    clonedElement.parentNode.insertBefore(wrapper, clonedElement);
                    wrapper.appendChild(clonedElement);
                    
                    // Add a company logo/text at the top
                    const logoContainer = clonedDoc.createElement('div');
                    logoContainer.style.textAlign = 'center';
                    logoContainer.style.marginBottom = '15px';
                    logoContainer.style.fontFamily = 'Arial, sans-serif';
                    logoContainer.style.fontSize = '28px';
                    logoContainer.style.fontWeight = 'bold';
                    logoContainer.style.color = document.documentElement.classList.contains('dark') ? '#f7fafc' : '#1a202c';
                    logoContainer.innerHTML = 'VSmart Systems';
                    wrapper.insertBefore(logoContainer, clonedElement);
                    
                    // Add a subtle watermark
                    const watermark = clonedDoc.createElement('div');
                    watermark.style.position = 'absolute';
                    watermark.style.bottom = '10px';
                    watermark.style.right = '20px';
                    watermark.style.fontFamily = 'Arial, sans-serif';
                    watermark.style.fontSize = '12px';
                    watermark.style.opacity = '0.5';
                    watermark.style.color = document.documentElement.classList.contains('dark') ? '#a0aec0' : '#718096';
                    watermark.innerHTML = 'Generated on ' + new Date().toLocaleDateString();
                    wrapper.appendChild(watermark);
                }
            }
        };
        
        // Capture the element as an image
        html2canvas(element, options).then(canvas => {
            // Convert to JPEG with maximum quality
            const dataUrl = canvas.toDataURL('image/jpeg', 1.0);
            
            // Create download link
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = 'VSmart-Feedback-{{ $feedback->id }}-' + new Date().toISOString().split('T')[0] + '.jpg';
            document.body.appendChild(link);
            
            // Trigger download
            link.click();
            
            // Restore original styles
            Object.assign(element.style, originalStyles);
            
            // Clean up
            document.body.removeChild(link);
            
            // Update success message
            toast.innerHTML = `
                <svg class="h-5 w-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="font-medium">Image downloaded successfully!</span>
            `;
            toast.classList.remove('bg-black');
            toast.classList.add('bg-green-700');
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('opacity-0');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 3000);
        }).catch(error => {
            console.error('Error taking screenshot:', error);
            
            // Restore original styles
            Object.assign(element.style, originalStyles);
            
            // Update error message
            toast.innerHTML = `
                <svg class="h-5 w-5 mr-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <span class="font-medium">Failed to generate image</span>
            `;
            toast.className = 'fixed top-4 right-4 bg-red-800 text-white px-5 py-3 rounded-lg shadow-xl z-50 flex items-center';
            
            // Remove error toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 3000);
        });
    }
</script>

<style>
    /* Styles for screenshot capture */
    #feedback-card {
        transform-origin: top left;
        will-change: transform;
    }
    
    /* Button hover animation */
    .hover\:scale-105:hover {
        transform: scale(1.05);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
    }
    
    /* Transition for all transform properties */
    .transition-all {
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 300ms;
    }
    
    /* Dark mode detection for JavaScript */
    @media (prefers-color-scheme: dark) {
        :root {
            color-scheme: dark;
        }
    }
</style>
@endsection 