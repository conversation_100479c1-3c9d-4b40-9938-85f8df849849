@extends('layouts.guest')

@section('content')
    <!-- Import Quicksand font -->
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .text-red-600 {
            transition: color 0.3s ease;
        }
        .auto-refresh-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 50;
        }
        .refresh-dot {
            height: 8px;
            width: 8px;
            background-color: #10B981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                opacity: 0.5;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(0.95);
                opacity: 0.5;
            }
        }
        
        /* Hide scrollbar but allow scrolling */
        .scrollbar-hide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;     /* Firefox */
            max-height: 80vh;          /* Maximum height on mobile */
        }
        
        /* Hide scrollbar for Chrome, Safari and Opera */
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
    </style>
    
    <!-- Auto-refresh indicator -->
    <div class="auto-refresh-indicator">
        <div class="refresh-dot"></div>
        <span>Auto-refreshing</span>
        <span id="countdown">30s</span>
    </div>

    <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 font-['Quicksand'] max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Service Status</h1>
                <p class="text-gray-600">Tracking Key: <span class="font-semibold">{{ $repair->special_key }}</span></p>
                <p class="text-gray-600">Customer: <span class="font-semibold">{{ $repair->items[0]->device->customer->name }}</span></p>
            </div>
            <form method="POST" action="{{ route('guest.logout') }}">
                @csrf
                <!-- <button type="submit" class="text-sm text-red-600 hover:text-red-700 underline">
                    Log Out
                </button> -->
            </form>
        </div>

        <!-- Status Card -->
        <div class="mb-8 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-800">Status Information</h2>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <div class="text-sm text-gray-500 mb-1">Status</div>
                        <div class="mb-3">
                            @php
                                $statusColors = [
                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                    'in_progress' => 'bg-blue-100 text-blue-800',
                                    'completed' => 'bg-green-100 text-green-800',
                                    'cancelled' => 'bg-red-100 text-red-800',
                                ];
                                $statusText = [
                                    'pending' => 'Pending',
                                    'in_progress' => 'In Progress',
                                    'completed' => 'Completed',
                                    'cancelled' => 'Cancelled',
                                ];
                                $statusColor = $statusColors[$repair->status] ?? 'bg-gray-100 text-gray-800';
                                $statusDisplay = $statusText[$repair->status] ?? ucfirst($repair->status);
                            @endphp
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColor }}">
                                {{ $statusDisplay }}
                            </span>
                        </div>

                        <!-- Payment Status -->
                        <div class="text-sm text-gray-500 mb-1">Payment Status</div>
                        <div class="mb-3">
                            @php
                                $paymentStatusColors = [
                                    'unpaid' => 'bg-red-100 text-red-800',
                                    'paid' => 'bg-green-100 text-green-800',
                                ];
                                $paymentStatusText = [
                                    'unpaid' => 'Unpaid',
                                    'paid' => 'Paid',
                                ];
                                $paymentStatusColor = $paymentStatusColors[$repair->payment_status ?? 'unpaid'] ?? 'bg-gray-100 text-gray-800';
                                $paymentStatusDisplay = $paymentStatusText[$repair->payment_status ?? 'unpaid'] ?? ucfirst($repair->payment_status ?? 'unpaid');
                            @endphp
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $paymentStatusColor }}" data-payment-status-indicator>
                                {{ $paymentStatusDisplay }}
                            </span>
                        </div>

                        @if($repair->status === 'pending')
                            <div class="text-sm text-gray-500 mb-1">Created On</div>
                            <div class="mb-3 font-medium">
                                {{ $repair->created_at->timezone('Asia/Manila')->format('M d, Y - h:i A') . ' PHT' }}
                            </div>
                        @else
                            <div class="text-sm text-gray-500 mb-1">Started At</div>
                            <div class="mb-3 font-medium">
                                {{ $repair->started_at ? $repair->started_at->timezone('Asia/Manila')->format('M d, Y - h:i A') . ' PHT' : 'Not started yet' }}
                            </div>
                        @endif

                        <div class="text-sm text-gray-500 mb-1">Duration</div>
                        <div class="mb-3 font-medium">
                            @if($repair->status === 'pending')
                                Not started yet
                            @elseif($repair->status === 'cancelled')
                                Cancelled
                            @elseif($repair->status === 'in_progress')
                                <div class="flex items-center">
                                    <span id="duration-timer">{{ $repair->duration }}</span>
                                    <span class="ml-2 flex h-2 w-2">
                                        <span class="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-red-400 opacity-75"></span>
                                        <span class="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                                    </span>
                                </div>
                            @else
                                {{ $repair->duration }}
                            @endif
                        </div>
                    </div>

                    <div>
                        <div class="text-sm text-gray-500 mb-1">Service Items</div>
                        @foreach ($repair->items as $item)
                            <div class="mb-3 p-3 bg-gray-50 rounded-lg">
                                <div class="font-medium">{{ $item->service->name }}</div>
                                <div class="text-sm text-gray-600">{{ $item->device->brand }} {{ $item->device->model }}</div>
                                <div class="mt-1 text-sm text-gray-500">
                                    Cost: ₱{{ number_format($item->cost, 2) }}
                                </div>
                                @if ($item->notes)
                                    <div class="mt-1 text-sm text-gray-500">
                                        Notes: {{ $item->notes }}
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-800 mb-4">Repair Progress</h2>
            <div class="relative">
                @php
                    $progressMap = [
                        'pending' => 1,
                        'in_progress' => 2,
                        'completed' => 3,
                        'cancelled' => 0,
                    ];
                    $currentProgress = $progressMap[$repair->status] ?? 0;
                    $progressWidth = '';
                    if ($currentProgress === 1) {
                        $progressWidth = 'w-1/3';
                    } elseif ($currentProgress === 2) {
                        $progressWidth = 'w-2/3';
                    } elseif ($currentProgress === 3) {
                        $progressWidth = 'w-full';
                    } else {
                        $progressWidth = 'w-0';
                    }
                @endphp
                
                <!-- Progress Line -->
                <div class="absolute top-5 left-0 w-full h-1 bg-gray-200"></div>
                <div class="absolute top-5 left-0 h-1 bg-red-600 {{ $progressWidth }}"></div>
                
                <!-- Steps -->
                <div class="relative flex justify-between">
                    <!-- Step 1: Pending -->
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 rounded-full {{ $currentProgress >= 1 ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-500' }} flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="text-sm font-medium mt-2">Pending</div>
                    </div>
                    
                    <!-- Step 2: In Progress -->
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 rounded-full {{ $currentProgress >= 2 ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-500' }} flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="text-sm font-medium mt-2">In Progress</div>
                    </div>
                    
                    <!-- Step 3: Completed -->
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 rounded-full {{ $currentProgress >= 3 ? 'bg-red-600 text-white' : 'bg-gray-200 text-gray-500' }} flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div class="text-sm font-medium mt-2">Completed</div>
                    </div>
                </div>
            </div>
        </div>

        @if($repair->notes)
            <!-- Notes Section -->
            <div class="mb-8">
                <h2 class="text-lg font-medium text-gray-800 mb-2">Repair Notes</h2>
                <div class="p-4 bg-gray-50 rounded-lg text-gray-700">
                    {{ $repair->notes }}
                </div>
            </div>
        @endif

        <!-- Back to tracking button -->
        <div class="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <a href="{{ route('guest.login') }}" class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Track Another Service
            </a>
            
            @if($repair->status !== 'pending' && $repair->status !== 'cancelled')
            <button type="button" 
                id="pay-button"
                @if($repair->payment_status === 'paid')
                    onclick="showAlreadyPaidModal()"
                    class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-400 cursor-pointer"
                @elseif($repair->status === 'completed')
                    onclick="showQRModal()"
                    class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                @else
                    onclick="showAdvancePaymentModal()"
                    class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                @endif
                >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
                PAY via QR
            </button>
            @endif
        </div>
    </div>

    <!-- Advance Payment Modal -->
    <div id="notCompletedModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full mx-auto">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 overflow-y-auto scrollbar-hide">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">
                                Advance Payment
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Pay in advance (₱{{ number_format($repair->total_cost, 2) }}) while your service is in progress.
                                </p>
                            </div>
                            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mt-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-yellow-700">
                                            Your service is still in progress.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="proceedToPayment()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Continue
                    </button>
                    <button type="button" onclick="hideNotCompletedModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Payment Modal -->
    <div id="qrModal" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full mx-auto">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 overflow-y-auto scrollbar-hide">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Payment: ₱{{ number_format($repair->total_cost, 2) }}
                            </h3>
                            
                            <div class="mt-4 grid grid-cols-1 gap-6">
                                @php
                                    $availablePayments = [];
                                    if($gcashEnabled) $availablePayments[] = 'gcash';
                                    if($paymayaEnabled) $availablePayments[] = 'maya';
                                @endphp

                                @if(count($availablePayments) > 0)
                                    <!-- Tabs for GCash and Maya -->
                                    <div class="flex border-b border-gray-200">
                                        @if($gcashEnabled)
                                            <button type="button" onclick="showQRTab('gcash')" id="gcash-tab" class="px-4 py-2 text-sm font-medium {{ $availablePayments[0] === 'gcash' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700' }}">GCash</button>
                                        @endif
                                        @if($paymayaEnabled)
                                            <button type="button" onclick="showQRTab('maya')" id="maya-tab" class="px-4 py-2 text-sm font-medium {{ $availablePayments[0] === 'maya' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700' }}">Maya</button>
                                        @endif
                                    </div>

                                    @if($gcashEnabled)
                                        <!-- GCash QR -->
                                        <div id="gcash-qr" class="flex flex-col items-center {{ $availablePayments[0] === 'gcash' ? '' : 'hidden' }}">
                                            <div class="border border-gray-300 rounded-lg p-4 mb-2 max-w-full">
                                                <img src="{{ asset('img/gcashQR.jpg') }}" alt="GCash QR Code" class="w-full max-w-[300px] h-auto mx-auto">
                                            </div>
                                            <p class="text-sm text-gray-600">Scan with GCash app</p>
                                            <button type="button" onclick="openFullScreenQR('{{ asset('img/gcashQR.jpg') }}')" class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                                                View Full Size
                                            </button>
                                        </div>
                                    @endif

                                    @if($paymayaEnabled)
                                        <!-- Maya QR -->
                                        <div id="maya-qr" class="flex flex-col items-center {{ $availablePayments[0] === 'maya' ? '' : 'hidden' }}">
                                            <div class="border border-gray-300 rounded-lg p-4 mb-2 max-w-full">
                                                <img src="{{ asset('img/mayaQR.jpg') }}" alt="Maya QR Code" class="w-full max-w-[300px] h-auto mx-auto">
                                            </div>
                                            <p class="text-sm text-gray-600">Scan with Maya app</p>
                                            <button type="button" onclick="openFullScreenQR('{{ asset('img/mayaQR.jpg') }}')" class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                                                View Full Size
                                            </button>
                                        </div>
                                    @endif
                                @else
                                    <!-- Cash Payment Only -->
                                    <div class="text-center py-8">
                                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                                            <svg class="mx-auto h-12 w-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                            </svg>
                                            <h3 class="mt-4 text-lg font-medium text-yellow-800">Cash Payment Only</h3>
                                            <p class="mt-2 text-sm text-yellow-700">
                                                Digital payment methods are currently unavailable. Please prepare cash payment of <strong>₱{{ number_format($repair->total_cost, 2) }}</strong> for when your device is ready for pickup.
                                            </p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="mt-6 bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-600 text-center">
                                    {{ $repair->status === 'completed' 
                                        ? 'Please show your receipt to the staff for verification.' 
                                        : 'Please show your receipt to the staff for verification.' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="hideQRModal()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Already Paid Modal -->
    <div id="alreadyPaidModal" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden">
        <div class="fixed inset-0 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-8 max-w-md w-full shadow-lg overflow-y-auto scrollbar-hide mx-4">
                <div class="text-center mb-4">
                    <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Payment Complete</h3>
                    <p class="text-gray-600 mt-2">This particular service has already been paid for.</p>
                </div>
                
                <div class="mt-4 bg-gray-50 p-5 rounded-lg border border-gray-200 shadow-sm">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between border-b border-gray-200 pb-3">
                            <span class="text-sm font-medium text-gray-700 flex items-center">
                                <svg class="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Payment Summary
                            </span>
                            <span class="bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-1 rounded-full flex items-center">
                                <svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Paid
                            </span>
                        </div>
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="text-sm text-gray-600 font-medium">Date</span>
                            </div>
                            <span class="text-sm text-gray-900 font-semibold">
                                {{ $repair->completed_at ? $repair->completed_at->format('F j, Y') : now()->format('F j, Y') }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between py-2 bg-gray-100 rounded-md px-3">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                </svg>
                                <span class="text-sm text-gray-600 font-medium">Amount</span>
                            </div>
                            <span class="text-sm text-gray-900 font-bold">
                                ₱{{ number_format($repair->total_cost, 2) }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                </svg>
                                <span class="text-sm text-gray-600 font-medium">Method</span>
                            </div>
                            <span class="text-sm text-gray-900 font-semibold px-2.5 py-0.5 rounded-full
                                {{ $repair->payment_method === 'gcash/maya' ? 'bg-blue-50 text-blue-700' : 
                                   ($repair->payment_method === 'cash' ? 'bg-green-50 text-green-700' : 'bg-purple-50 text-purple-700') }}">
                                {{ $repair->payment_method === 'gcash/maya' ? 'GCash/Maya' : ucfirst($repair->payment_method ?? 'N/A') }}
                            </span>
                        </div>
                        <div class="mt-2 pt-3 border-t border-gray-200">
                            <div class="text-xs text-gray-500 italic flex items-center">
                                <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Don't forget to ask for your receipt when completed
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button type="button" onclick="hideAlreadyPaidModal()" class="w-full inline-flex justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    @if($repair->status === 'in_progress')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Store the start time as ISO format string from Manila timezone
            const startTimeStr = "{{ $repair->started_at->timezone('Asia/Manila')->toIso8601String() }}";
            const startedAt = new Date(startTimeStr);
            const durationTimer = document.getElementById('duration-timer');
            
            // Log for debugging
            console.log('Start time (Manila):', startTimeStr);
            console.log('Parsed JS Date:', startedAt.toISOString());
            
            // Check if startedAt is a valid date
            if (isNaN(startedAt.getTime())) {
                console.error('Invalid start date');
                return;
            }
            
            function updateDuration() {
                try {
                    // Get current time in UTC
                    const now = new Date();
                    
                    // Calculate duration in milliseconds
                    const durationMs = now.getTime() - startedAt.getTime();
                    
                    // Handle negative duration (can happen if server time is ahead of client time)
                    const adjustedDurationMs = Math.max(0, durationMs);
                    
                    // Calculate days, hours, minutes, seconds
                    const days = Math.floor(adjustedDurationMs / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((adjustedDurationMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((adjustedDurationMs % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((adjustedDurationMs % (1000 * 60)) / 1000);
                    
                    // Format the duration
                    const parts = [];
                    
                    if (days > 0) {
                        parts.push(days + (days === 1 ? ' day' : ' days'));
                    }
                    
                    if (hours > 0) {
                        parts.push(hours + (hours === 1 ? ' hour' : ' hours'));
                    }
                    
                    if (minutes > 0) {
                        parts.push(minutes + (minutes === 1 ? ' minute' : ' minutes'));
                    }
                    
                    if (seconds > 0 || parts.length === 0) {
                        parts.push(seconds + (seconds === 1 ? ' second' : ' seconds'));
                    }
                    
                    // Join with commas and 'and' for the last part
                    let formattedDuration = '';
                    if (parts.length > 1) {
                        const lastPart = parts.pop();
                        formattedDuration = parts.join(', ') + ' and ' + lastPart;
                    } else {
                        formattedDuration = parts[0];
                    }
                    
                    // Update the timer display with a subtle animation
                    if (durationTimer.textContent !== formattedDuration) {
                        durationTimer.textContent = formattedDuration;
                        durationTimer.classList.add('text-red-600');
                        setTimeout(() => {
                            durationTimer.classList.remove('text-red-600');
                        }, 300);
                    }
                } catch (e) {
                    console.error('Error updating duration:', e);
                }
            }
            
            // Update immediately
            updateDuration();
            
            // Then update every second
            const intervalId = setInterval(updateDuration, 1000);
            
            // Cleanup on page unload
            window.addEventListener('beforeunload', function() {
                clearInterval(intervalId);
            });
        });
    </script>
    @endif

    <script>
        // Auto-refresh functionality
        function startAutoRefresh() {
            const REFRESH_INTERVAL = 30; // seconds
            let timeLeft = REFRESH_INTERVAL;
            const countdownElement = document.getElementById('countdown');
            
            function updateCountdown() {
                countdownElement.textContent = timeLeft + 's';
                if (timeLeft <= 0) {
                    window.location.reload();
                } else {
                    timeLeft--;
                    setTimeout(updateCountdown, 1000);
                }
            }
            
            updateCountdown();
        }

        // Initialize the payment button based on current payment status
        function initializePaymentButton() {
            const payButton = document.getElementById('pay-button');
            if (payButton) {
                const paymentStatus = '{{ $repair->payment_status ?? 'unpaid' }}';
                const repairStatus = '{{ $repair->status }}';
                
                console.log('Initial payment status:', paymentStatus);
                console.log('Initial repair status:', repairStatus);
                
                if (paymentStatus === 'paid') {
                    // If paid, grey out button regardless of repair status
                    payButton.classList.remove('bg-green-600', 'hover:bg-green-700', 'bg-blue-600', 'hover:bg-blue-700');
                    payButton.classList.add('bg-gray-400');
                    payButton.setAttribute('onclick', 'showAlreadyPaidModal()');
                }
            }
        }

        // Start auto-refresh and initialize button when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            startAutoRefresh();
            initializePaymentButton();
            // Initial fetch of status
            fetchLatestStatus();
        });

        // Fetch latest repair status from server
        function fetchLatestStatus() {
            fetch('/guest/repair-status?key={{ $repair->special_key }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI with new data
                        updateRepairStatus(data.repair);
                    }
                })
                .catch(error => console.error('Error fetching status:', error));
        }
        
        // Set up interval to fetch status updates every 15 seconds
        setInterval(fetchLatestStatus, 15000);

        // QR Modal functionality
        function showQRModal() {
            const modal = document.getElementById('qrModal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            }
        }

        function hideQRModal() {
            const qrModal = document.getElementById('qrModal');
            if (qrModal) {
                qrModal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Advance Payment modal functionality
        function showAdvancePaymentModal() {
            const modal = document.getElementById('notCompletedModal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            }
        }

        function hideNotCompletedModal() {
            const modal = document.getElementById('notCompletedModal');
            if (modal) {
                modal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // Proceed directly to payment
        function proceedToPayment() {
            hideNotCompletedModal();
            showQRModal();
        }

        // QR Tab switching functionality
        function showQRTab(tabName) {
            // Hide all QR sections
            document.getElementById('gcash-qr').classList.add('hidden');
            document.getElementById('maya-qr').classList.add('hidden');
            
            // Show selected QR section
            document.getElementById(`${tabName}-qr`).classList.remove('hidden');
            
            // Update tab styles
            document.getElementById('gcash-tab').classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
            document.getElementById('gcash-tab').classList.add('text-gray-500');
            document.getElementById('maya-tab').classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
            document.getElementById('maya-tab').classList.add('text-gray-500');
            
            // Style selected tab
            document.getElementById(`${tabName}-tab`).classList.remove('text-gray-500');
            document.getElementById(`${tabName}-tab`).classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        }

        // Full screen QR view functionality
        function openFullScreenQR(imageUrl) {
            // Create full screen container
            const container = document.createElement('div');
            container.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90';
            container.onclick = function() {
                document.body.removeChild(container);
                document.body.classList.remove('overflow-hidden');
            };

            // Create image element
            const img = document.createElement('img');
            img.src = imageUrl;
            img.className = 'max-w-[90vw] max-h-[90vh] object-contain';
            img.onclick = function(e) {
                e.stopPropagation();
            };
            
            // Create close button
            const closeBtn = document.createElement('button');
            closeBtn.className = 'absolute top-4 right-4 bg-white rounded-full p-2 text-gray-800 hover:text-gray-600';
            closeBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>';
            closeBtn.onclick = function(e) {
                e.stopPropagation();
                document.body.removeChild(container);
                document.body.classList.remove('overflow-hidden');
            };

            // Add elements to container
            container.appendChild(img);
            container.appendChild(closeBtn);
            document.body.appendChild(container);
            document.body.classList.add('overflow-hidden');
        }

        // Update repair status display
        function updateRepairStatus(repair) {
            // Update status display
            const statusColors = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'in_progress': 'bg-blue-100 text-blue-800',
                'completed': 'bg-green-100 text-green-800',
                'cancelled': 'bg-red-100 text-red-800'
            };
            
            const statusText = {
                'pending': 'Pending',
                'in_progress': 'In Progress',
                'completed': 'Completed',
                'cancelled': 'Cancelled'
            };
            
            const paymentStatusColors = {
                'unpaid': 'bg-red-100 text-red-800',
                'paid': 'bg-green-100 text-green-800'
            };
            
            const paymentStatusText = {
                'unpaid': 'Unpaid',
                'paid': 'Paid'
            };
            
            // Get all status elements
            const statusElement = document.querySelector('[class*="text-xs font-medium bg-"]');
            const paymentStatusElement = document.querySelector('[data-payment-status-indicator]');
            
            if (statusElement) {
                // Remove old color classes
                statusElement.className = statusElement.className.replace(/bg-\w+-\d+ text-\w+-\d+/, '');
                // Add new color classes
                statusElement.className += ' ' + (statusColors[repair.status] || 'bg-gray-100 text-gray-800');
                // Update text
                statusElement.textContent = statusText[repair.status] || repair.status;
            }
            
            if (paymentStatusElement) {
                // Remove old color classes
                paymentStatusElement.className = paymentStatusElement.className.replace(/bg-\w+-\d+ text-\w+-\d+/, '');
                // Add new color classes
                paymentStatusElement.className += ' ' + (paymentStatusColors[repair.payment_status] || 'bg-gray-100 text-gray-800');
                // Update text
                paymentStatusElement.textContent = paymentStatusText[repair.payment_status] || repair.payment_status;
            }
            
            // Update PAY button based on payment status
            updatePayButton(repair.payment_status, repair.status);
            
            // Update other elements as needed
            updateRepairDetailsUI(repair);
        }

        // Helper function to update the payment button
        function updatePayButton(paymentStatus, repairStatus) {
            const payButton = document.getElementById('pay-button');
            if (payButton) {
                if (paymentStatus === 'paid') {
                    // If paid, grey out button regardless of repair status
                    payButton.classList.remove('bg-green-600', 'hover:bg-green-700', 'bg-blue-600', 'hover:bg-blue-700');
                    payButton.classList.add('bg-gray-400');
                    payButton.setAttribute('onclick', 'showAlreadyPaidModal()');
                } else if (repairStatus === 'completed') {
                    // If completed but not paid, show green pay button
                    payButton.classList.remove('bg-gray-400', 'bg-blue-600', 'hover:bg-blue-700');
                    payButton.classList.add('bg-green-600', 'hover:bg-green-700');
                    payButton.setAttribute('onclick', 'showQRModal()');
                } else {
                    // If in progress and not paid, show blue advance payment button
                    payButton.classList.remove('bg-gray-400', 'bg-green-600', 'hover:bg-green-700');
                    payButton.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    payButton.setAttribute('onclick', 'showAdvancePaymentModal()');
                }
            }
        }
        
        // Helper function to update other UI elements based on repair data
        function updateRepairDetailsUI(repair) {
            // Update start date if available
            const startedAtElement = document.querySelector('[data-started-at]');
            if (startedAtElement && repair.started_at) {
                startedAtElement.textContent = repair.started_at;
            }
            
            // Update completed date if available
            const completedAtElement = document.querySelector('[data-completed-at]');
            if (completedAtElement && repair.completed_at) {
                completedAtElement.textContent = repair.completed_at;
            }
            
            // Update notes if changed
            const notesElement = document.querySelector('[data-repair-notes]');
            if (notesElement && repair.notes) {
                notesElement.textContent = repair.notes;
            }
        }
        
        // Already paid modal functionality
        function showAlreadyPaidModal() {
            const modal = document.getElementById('alreadyPaidModal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            }
        }

        function hideAlreadyPaidModal() {
            const modal = document.getElementById('alreadyPaidModal');
            if (modal) {
                modal.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }
    </script>
@endpush 