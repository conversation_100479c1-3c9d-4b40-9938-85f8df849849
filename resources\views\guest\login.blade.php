@extends('layouts.guest')

@section('content')
    <!-- Import Quicksand font -->
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" href="{{ asset('img/LogoClear.png') }}">

    
    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-100 font-['Quicksand']">
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Service Status Tracking</h1>
            <p class="text-gray-600 mt-1">Enter your service tracking key to view status</p>
        </div>

        @if(session('error'))
            <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-red-700">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if(session('success'))
            <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-green-700">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <form method="POST" action="{{ route('guest.login.submit') }}" class="space-y-6">
            @csrf

            <!-- Tracking Key -->
            <div>
                <x-input-label for="special_key" :value="__('Tracking Key')" class="text-sm font-medium text-gray-700 mb-1" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                    </div>
                    <x-text-input id="special_key" 
                        class="block w-full pl-12 pr-4 py-3 border-gray-200 rounded-lg focus:ring-2 focus:ring-red-100 focus:border-red-600 transition duration-200 uppercase" 
                        type="text" 
                        name="special_key" 
                        :value="old('special_key')" 
                        required 
                        autofocus 
                        placeholder="Enter Tracking Key (e.g., ABC12345)"
                        maxlength="8" />
                </div>
                <x-input-error :messages="$errors->get('special_key')" class="mt-1" />
                <p class="mt-1 text-xs text-gray-500">You can find your tracking key on your repair receipt or ask customer service</p>
            </div>

            <div>
                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-200">
                    {{ __('Track My Repair') }}
                </button>
            </div>
        </form>
    </div>

    <div class="mt-6 text-center">
        <p class="text-sm text-gray-600 font-['Quicksand']">
            {{ __("Already have an account?") }}
            <a href="{{ route('login') }}" class="font-medium text-red-600 hover:text-red-700">
                {{ __('Sign in here') }}
            </a>
        </p>
    </div>
@endsection