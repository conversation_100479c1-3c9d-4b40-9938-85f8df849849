@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="flex flex-col space-y-4 mb-6">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h1>
            <div class="flex space-x-2">
                <span id="notification-count" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    <svg class="mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3" />
                    </svg>
                    Loading...
                </span>
                <button 
                    id="refresh-notifications-btn"
                    class="inline-flex items-center px-3 py-1.5 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition"
                >
                    <svg class="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
                <!-- <button 
                    id="mark-all-read-btn"
                    class="inline-flex items-center px-3 py-1.5 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition"
                >
                    <svg class="mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Mark all as read
                </button> -->
            </div>
        </div>
        
        <!-- Filter bar -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-3">
            <div class="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-4">
                <div class="flex space-x-2">
                    <button 
                        data-filter="all" 
                        class="filter-btn active inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 border border-transparent rounded-md font-medium text-xs transition"
                    >
                        All
                    </button>
                    <button 
                        data-filter="pending" 
                        class="filter-btn inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition"
                    >
                        <span class="w-2 h-2 bg-yellow-500 rounded-full mr-1.5"></span>
                        Pending
                    </button>
                    <button 
                        data-filter="in_progress" 
                        class="filter-btn inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition"
                    >
                        <span class="w-2 h-2 bg-blue-500 rounded-full mr-1.5"></span>
                        In Progress
                    </button>
                    <button 
                        data-filter="unpaid" 
                        class="filter-btn inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition"
                    >
                        <span class="w-2 h-2 bg-red-500 rounded-full mr-1.5"></span>
                        Unpaid
                    </button>
                </div>
                <div class="flex items-center">
                    <label for="sort-select" class="text-xs text-gray-600 dark:text-gray-400 mr-2">Sort:</label>
                    <select id="sort-select" class="text-xs bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <option value="newest">Newest first</option>
                        <option value="oldest">Oldest first</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div id="notifications-loading" class="p-12 flex flex-col justify-center items-center">
            <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">Loading notifications...</p>
        </div>

        <div id="notifications-container" class="hidden">
            <!-- Category headers will be added dynamically when needed -->
            <div id="today-notifications" class="hidden">
                <div class="bg-gray-50 dark:bg-gray-750 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Today</h3>
                </div>
                <div class="today-items divide-y divide-gray-200 dark:divide-gray-700"></div>
            </div>
            
            <div id="yesterday-notifications" class="hidden">
                <div class="bg-gray-50 dark:bg-gray-750 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Yesterday</h3>
                </div>
                <div class="yesterday-items divide-y divide-gray-200 dark:divide-gray-700"></div>
            </div>
            
            <div id="older-notifications" class="hidden">
                <div class="bg-gray-50 dark:bg-gray-750 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Older</h3>
                </div>
                <div class="older-items divide-y divide-gray-200 dark:divide-gray-700"></div>
            </div>
        </div>

        <div id="notifications-empty" class="hidden py-16 px-4 text-center">
            <div class="mx-auto h-16 w-16 text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
            </div>
            <h3 class="mt-4 text-base font-medium text-gray-900 dark:text-white">No notifications</h3>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">You're all caught up! There are no notifications to display.</p>
        </div>
        
        <div id="notifications-filtered-empty" class="hidden py-16 px-4 text-center">
            <div class="mx-auto h-16 w-16 text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
            </div>
            <h3 class="mt-4 text-base font-medium text-gray-900 dark:text-white">No matching notifications</h3>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Try changing your filter settings to see more notifications.</p>
        </div>

        <div id="notifications-error" class="hidden py-16 px-4 text-center">
            <div class="mx-auto h-16 w-16 text-red-400 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
            </div>
            <h3 class="mt-4 text-base font-medium text-gray-900 dark:text-white">Error loading notifications</h3>
            <p id="error-message" class="mt-2 text-sm text-gray-500 dark:text-gray-400">There was a problem loading your notifications. Please try again.</p>
            <div class="mt-6">
                <button onclick="fetchAllNotifications()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition">
                    <svg class="mr-2 -ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Try Again
                </button>
            </div>
        </div>
    </div>
    
    <!-- Pagination controls -->
    <div id="pagination-container" class="hidden mt-4 flex justify-between items-center">
        <div class="text-sm text-gray-700 dark:text-gray-300">
            Showing <span id="pagination-start">1</span>-<span id="pagination-end">10</span> of <span id="pagination-total">0</span>
        </div>
        <div class="flex space-x-2">
            <button id="prev-page" class="inline-flex items-center px-3 py-1.5 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Previous
            </button>
            <button id="next-page" class="inline-flex items-center px-3 py-1.5 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 border border-transparent rounded-md font-medium text-xs text-gray-700 dark:text-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed">
                Next
                <svg class="ml-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Global variables
    let allNotifications = [];
    let currentFilter = 'all';
    let currentSort = 'newest';
    let currentPage = 1;
    const pageSize = 10;
    let isLoading = false;
    
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize notification count to avoid "Loading..." text
        document.getElementById('notification-count').innerHTML = `
            <svg class="mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                <circle cx="4" cy="4" r="3" />
            </svg>
            0 unread
        `;
        
        // Always fetch notifications on page load
        fetchAllNotifications();
        
        // Check if we were directed here from the notification bell using URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('source') === 'bell') {
            // Remove the parameter from URL without reloading
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('source');
            window.history.replaceState({}, document.title, newUrl);
            
            // We've come from the notification bell
            fetchAllNotifications();
        }
        
        // Set up event listeners
        document.getElementById('mark-all-read-btn').addEventListener('click', function() {
            if (!isLoading) {
                markAllAsRead();
            }
        });
        
        // Add refresh button event listener
        document.getElementById('refresh-notifications-btn').addEventListener('click', function() {
            if (!isLoading) {
                fetchAllNotifications();
            }
        });
        
        // Handle SPA navigation - listen for custom event
        window.addEventListener('notificationBellClicked', function() {
            // We've navigated from the notification bell in SPA mode
            if (!isLoading) {
                fetchAllNotifications();
            }
        });
        
        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(button => {
            button.addEventListener('click', function() {
                // Update active state
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active', 'bg-blue-100', 'text-blue-800', 'dark:bg-blue-900', 'dark:text-blue-300');
                    btn.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-200');
                });
                this.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-200');
                this.classList.add('active', 'bg-blue-100', 'text-blue-800', 'dark:bg-blue-900', 'dark:text-blue-300');
                
                // Apply filter
                currentFilter = this.getAttribute('data-filter');
                currentPage = 1;
                applyFiltersAndRender();
            });
        });
        
        // Sort select
        document.getElementById('sort-select').addEventListener('change', function() {
            currentSort = this.value;
            currentPage = 1;
            applyFiltersAndRender();
        });
        
        // Pagination
        document.getElementById('prev-page').addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                applyFiltersAndRender();
            }
        });
        
        document.getElementById('next-page').addEventListener('click', function() {
            const totalPages = Math.ceil(getFilteredNotifications().length / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                applyFiltersAndRender();
            }
        });
    });
    
    function fetchAllNotifications() {
        if (isLoading) return;
        
        isLoading = true;
        
        // Show loading, hide other states
        document.getElementById('notifications-loading').classList.remove('hidden');
        document.getElementById('notifications-container').classList.add('hidden');
        document.getElementById('notifications-empty').classList.add('hidden');
        document.getElementById('notifications-filtered-empty').classList.add('hidden');
        document.getElementById('notifications-error').classList.add('hidden');
        document.getElementById('pagination-container').classList.add('hidden');
        
        fetch('/notifications/data', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Server error: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // Cache the data for SPA navigation
            window.notificationsData = data;
            
            // Hide loading
            document.getElementById('notifications-loading').classList.add('hidden');
            
            // Process and store all notifications
            processNotifications(data);
            
            // Apply filters and render
            applyFiltersAndRender();
            
            isLoading = false;
        })
        .catch(error => {
            console.error('Error fetching notifications:', error);
            
            // Hide loading, show error
            document.getElementById('notifications-loading').classList.add('hidden');
            document.getElementById('notifications-error').classList.remove('hidden');
            document.getElementById('error-message').textContent = error.message || 'There was a problem loading your notifications.';
            
            isLoading = false;
        });
    }
    
    function processNotifications(data) {
        // Reset the array
        allNotifications = [];
        
        // Add system notifications
        if (data.notifications && data.notifications.length) {
            data.notifications.forEach(notification => {
                allNotifications.push({
                    type: 'system',
                    data: notification,
                    created_at: notification.created_at,
                    read: notification.read_at !== null
                });
            });
        }
        
        // Add repair notifications with their specific type
        if (data.pending_repairs && data.pending_repairs.length) {
            data.pending_repairs.forEach(repair => {
                allNotifications.push({
                    type: 'repair',
                    subtype: 'pending_repair',
                    data: repair,
                    created_at: repair.created_at,
                    read: false // Repairs are always considered "unread"
                });
            });
        }
        
        if (data.in_progress_repairs && data.in_progress_repairs.length) {
            data.in_progress_repairs.forEach(repair => {
                allNotifications.push({
                    type: 'repair',
                    subtype: 'in_progress_repair',
                    data: repair,
                    created_at: repair.created_at,
                    read: false
                });
            });
        }
        
        if (data.unpaid_repairs && data.unpaid_repairs.length) {
            data.unpaid_repairs.forEach(repair => {
                allNotifications.push({
                    type: 'repair',
                    subtype: 'unpaid_repair',
                    data: repair,
                    created_at: repair.created_at,
                    read: false
                });
            });
        }
        
        // Update notification count
        updateNotificationCount();
    }
    
    function updateNotificationCount() {
        const unreadCount = allNotifications.filter(notification => !notification.read).length;
        const countElement = document.getElementById('notification-count');
        
        if (unreadCount > 0) {
            countElement.innerHTML = `
                <svg class="mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                    <circle cx="4" cy="4" r="3" />
                </svg>
                ${unreadCount} unread
            `;
        } else {
            countElement.innerHTML = 'All read';
            countElement.classList.remove('bg-blue-100', 'text-blue-800', 'dark:bg-blue-900', 'dark:text-blue-300');
            countElement.classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-300');
        }
    }
    
    function getFilteredNotifications() {
        // Apply filters
        let filtered = [...allNotifications];
        
        if (currentFilter === 'pending') {
            filtered = filtered.filter(notification => 
                notification.type === 'repair' && notification.subtype === 'pending_repair'
            );
        } else if (currentFilter === 'in_progress') {
            filtered = filtered.filter(notification => 
                notification.type === 'repair' && notification.subtype === 'in_progress_repair'
            );
        } else if (currentFilter === 'unpaid') {
            filtered = filtered.filter(notification => 
                notification.type === 'repair' && notification.subtype === 'unpaid_repair'
            );
        }
        
        // Apply sorting
        filtered.sort((a, b) => {
            const dateA = new Date(a.created_at);
            const dateB = new Date(b.created_at);
            
            if (currentSort === 'newest') {
                return dateB - dateA;
            } else {
                return dateA - dateB;
            }
        });
        
        return filtered;
    }
    
    function applyFiltersAndRender() {
        const filtered = getFilteredNotifications();
        
        // Clear all containers
        document.querySelector('.today-items').innerHTML = '';
        document.querySelector('.yesterday-items').innerHTML = '';
        document.querySelector('.older-items').innerHTML = '';
        
        // Hide all category sections initially
        document.getElementById('today-notifications').classList.add('hidden');
        document.getElementById('yesterday-notifications').classList.add('hidden');
        document.getElementById('older-notifications').classList.add('hidden');
        
        if (filtered.length === 0) {
            // Show empty state based on filter
            if (currentFilter !== 'all' && allNotifications.length > 0) {
                document.getElementById('notifications-filtered-empty').classList.remove('hidden');
            } else {
                document.getElementById('notifications-empty').classList.remove('hidden');
            }
            document.getElementById('notifications-container').classList.add('hidden');
            document.getElementById('pagination-container').classList.add('hidden');
            return;
        }
        
        // Get current page items
        const start = (currentPage - 1) * pageSize;
        const end = Math.min(start + pageSize, filtered.length);
        const paginatedItems = filtered.slice(start, end);
        
        // Update pagination info
        document.getElementById('pagination-start').textContent = start + 1;
        document.getElementById('pagination-end').textContent = end;
        document.getElementById('pagination-total').textContent = filtered.length;
        
        // Enable/disable pagination buttons
        document.getElementById('prev-page').disabled = currentPage === 1;
        document.getElementById('next-page').disabled = end === filtered.length;
        
        // Show pagination if needed
        if (filtered.length > pageSize) {
            document.getElementById('pagination-container').classList.remove('hidden');
        } else {
            document.getElementById('pagination-container').classList.add('hidden');
        }
        
        // Categorize notifications by date
        const now = new Date();
        now.setHours(0, 0, 0, 0);
        
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        
        const todayNotifications = [];
        const yesterdayNotifications = [];
        const olderNotifications = [];
        
        paginatedItems.forEach(notification => {
            const date = new Date(notification.created_at);
            date.setHours(0, 0, 0, 0);
            
            if (date.getTime() === now.getTime()) {
                todayNotifications.push(notification);
            } else if (date.getTime() === yesterday.getTime()) {
                yesterdayNotifications.push(notification);
            } else {
                olderNotifications.push(notification);
            }
        });
        
        // Render each category if it has items
        if (todayNotifications.length > 0) {
            document.getElementById('today-notifications').classList.remove('hidden');
            renderNotificationGroup(todayNotifications, '.today-items');
        }
        
        if (yesterdayNotifications.length > 0) {
            document.getElementById('yesterday-notifications').classList.remove('hidden');
            renderNotificationGroup(yesterdayNotifications, '.yesterday-items');
        }
        
        if (olderNotifications.length > 0) {
            document.getElementById('older-notifications').classList.remove('hidden');
            renderNotificationGroup(olderNotifications, '.older-items');
        }
        
        // Show the container
        document.getElementById('notifications-container').classList.remove('hidden');
    }
    
    function renderNotificationGroup(notifications, containerSelector) {
        const container = document.querySelector(containerSelector);
        
        notifications.forEach(item => {
            let element;
            
            if (item.type === 'system') {
                element = createSystemNotificationElement(item.data);
            } else if (item.type === 'repair') {
                if (item.subtype === 'pending_repair') {
                    element = createRepairNotificationElement(item.data, 'pending');
                } else if (item.subtype === 'in_progress_repair') {
                    element = createRepairNotificationElement(item.data, 'in_progress');
                } else if (item.subtype === 'unpaid_repair') {
                    element = createRepairNotificationElement(item.data, 'unpaid');
                }
            }
            
            if (element) {
                container.appendChild(element);
            }
        });
    }
    
    function createSystemNotificationElement(notification) {
        let notificationData = {};
        
        try {
            if (notification.data) {
                notificationData = notification.data;
            }
        } catch (e) {
            console.error('Error parsing notification data:', e);
        }
        
        const element = document.createElement('div');
        element.className = `p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition${notification.read_at ? '' : ' bg-blue-50 dark:bg-blue-900/20'}`;
        
        // Determine notification type and render appropriate content
        if (notification.type && notification.type.includes('SalesReportReminderNotification')) {
            element.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                            <svg class="h-5 w-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                ${notificationData.period ? (notificationData.period === 'weekly' ? 'Weekly' : 'Monthly') : 'Sales'} Report is now available
                            </p>
                            <button 
                                onclick="markNotificationRead('${notification.id}')"
                                class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                            >
                                ${notification.read_at ? '•••' : 'Mark as read'}
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                            ${notificationData.message || 'Your sales report is ready to view.'}
                        </p>
                        <div class="mt-2 flex">
                            ${notificationData.report_url ? `
                                <a href="${notificationData.report_url}" class="inline-flex items-center px-2.5 py-1.5 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-800/30 border border-blue-200 dark:border-blue-800 rounded text-xs font-medium text-blue-700 dark:text-blue-400 transition">
                                    <svg class="mr-1.5 -ml-0.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    View Report
                                </a>
                            ` : ''}
                        </div>
                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            ${formatDate(notification.created_at)}
                        </p>
                    </div>
                </div>
            `;
        } else {
            // Generic notification
            element.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                            <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                System Notification
                            </p>
                            <button 
                                onclick="markNotificationRead('${notification.id}')"
                                class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                            >
                                ${notification.read_at ? '•••' : 'Mark as read'}
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                            ${notificationData.message || 'You have a new system notification.'}
                        </p>
                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            ${formatDate(notification.created_at)}
                        </p>
                    </div>
                </div>
            `;
        }
        
        return element;
    }
    
    function createRepairNotificationElement(repair, status) {
        // Get repair details
        let deviceInfo = 'Unknown device';
        let customerInfo = 'Unknown customer';
        let serviceInfo = 'Unknown service';
        
        if (repair.items && repair.items.length > 0) {
            const item = repair.items[0];
            if (item && item.device) {
                deviceInfo = [item.device.brand, item.device.model].filter(Boolean).join(' ') || 'Unknown device';
                if (item.device.customer) {
                    customerInfo = item.device.customer.name || 'Unknown customer';
                }
            }
            if (item && item.service) {
                serviceInfo = item.service.name || 'Unknown service';
            }
        }
        
        // Set icon and color based on status
        let bgColor, iconColor, icon, title, statusBadge;
        
        if (status === 'pending') {
            bgColor = 'bg-yellow-100 dark:bg-yellow-900/30';
            iconColor = 'text-yellow-600 dark:text-yellow-400';
            icon = '<path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12zm-1-5a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1zm1-7a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />';
            title = 'New repair request';
            statusBadge = '<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300">Pending</span>';
        } else if (status === 'in_progress') {
            bgColor = 'bg-blue-100 dark:bg-blue-900/30';
            iconColor = 'text-blue-600 dark:text-blue-400';
            icon = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />';
            title = 'Repair in progress';
            statusBadge = '<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300">In Progress</span>';
        } else if (status === 'unpaid') {
            bgColor = 'bg-red-100 dark:bg-red-900/30';
            iconColor = 'text-red-600 dark:text-red-400';
            icon = '<path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />';
            title = 'Payment required';
            statusBadge = '<span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300">Unpaid</span>';
        } else {
            bgColor = 'bg-gray-100 dark:bg-gray-700';
            iconColor = 'text-gray-600 dark:text-gray-400';
            icon = '<path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12z" clip-rule="evenodd" />';
            title = 'Repair notification';
            statusBadge = '';
        }
        
        // Create cost info for unpaid repairs
        let costInfo = '';
        if (status === 'unpaid' && repair.total_cost) {
            try {
                const cost = parseFloat(repair.total_cost);
                costInfo = `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-300 ml-2">₱${cost.toFixed(2)}</span>`;
            } catch (e) {
                console.warn('Could not parse cost:', e);
            }
        }
        
        const element = document.createElement('div');
        element.className = 'p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition';
        
        element.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full ${bgColor} flex items-center justify-center">
                        <svg class="h-5 w-5 ${iconColor}" fill="currentColor" viewBox="0 0 20 20">
                            ${icon}
                        </svg>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                            ${title}
                        </p>
                        ${statusBadge}
                        ${costInfo}
                    </div>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                        <span class="font-medium">${deviceInfo}</span> • ${customerInfo} • ${serviceInfo}
                    </p>
                    <div class="mt-2">
                        <a href="/repairs/${repair.id}" class="inline-flex items-center px-2.5 py-1.5 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-800/30 border border-blue-200 dark:border-blue-800 rounded text-xs font-medium text-blue-700 dark:text-blue-400 transition">
                            <svg class="mr-1.5 -ml-0.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            View Details
                        </a>
                    </div>
                    <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        ${formatDate(repair.created_at)}
                    </p>
                </div>
            </div>
        `;
        
        return element;
    }
    
    function markNotificationRead(id) {
        const formData = new FormData();
        formData.append('notification_id', id);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        
        fetch('/notifications/mark-read', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                fetchAllNotifications();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    }
    
    function markAllAsRead() {
        if (isLoading) return;
        
        isLoading = true;
        const markButton = document.getElementById('mark-all-read-btn');
        const originalText = markButton.innerHTML;
        
        // Show loading state on the button
        markButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700 dark:text-gray-200" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
        `;
        markButton.disabled = true;
        
        const formData = new FormData();
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        
        fetch('/notifications/mark-all-read', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                fetchAllNotifications();
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
            isLoading = false;
            markButton.innerHTML = originalText;
            markButton.disabled = false;
        });
    }
    
    function formatDate(dateString) {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffSec = Math.floor(diffMs / 1000);
            const diffMin = Math.floor(diffSec / 60);
            const diffHour = Math.floor(diffMin / 60);
            const diffDay = Math.floor(diffHour / 24);
            
            // Format relative time
            if (diffSec < 60) {
                return 'Just now';
            } else if (diffMin < 60) {
                return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
            } else if (diffHour < 24) {
                return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
            } else if (diffDay === 1) {
                return 'Yesterday';
            } else if (diffDay < 7) {
                return `${diffDay} days ago`;
            } else {
                // Format date nicely
                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                const month = months[date.getMonth()];
                const day = date.getDate();
                return `${month} ${day}`;
            }
        } catch (e) {
            return dateString;
        }
    }
</script>
@endpush
@endsection 