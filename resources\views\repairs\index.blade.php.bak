@extends('layouts.app')

@section('head')
<style>
    /* Modal animation styles */
    #modalContent {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .modal-overlay {
        transition: opacity 0.3s ease;
    }
    
    /* Form animations */
    .repair-item {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Button hover effects */
    .btn-hover-effect {
        transition: all 0.2s ease;
    }
    
    .btn-hover-effect:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    /* Dark mode enhancements */
    .dark .modal-content {
        background-color: #1f2937;
        color: #f3f4f6;
    }
    
    .dark .modal-header {
        border-bottom-color: #374151;
    }
    
    .dark .modal-footer {
        border-top-color: #374151;
    }
    
    .dark input, 
    .dark select, 
    .dark textarea {
        background-color: #374151 !important;
        color: #f3f4f6 !important;
        border-color: #4b5563 !important;
    }
    
    .dark input::placeholder, 
    .dark textarea::placeholder {
        color: #9ca3af !important;
    }
    
    .dark label {
        color: #e5e7eb !important;
    }
    
    /* Enhanced UI styles */
    .status-badge {
        @apply px-3 py-1 text-xs font-medium rounded-full;
        transition: all 0.2s ease;
    }
    
    .status-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .table-row-hover {
        @apply transition-all duration-200;
    }
    
    .table-row-hover:hover {
        @apply bg-gray-50 dark:bg-gray-700 transform scale-[1.01] shadow-sm;
    }
    
    .table-row-hover td {
        @apply transition-colors duration-200;
    }
    
    .table-row-hover:hover td {
        @apply text-gray-900 dark:text-white;
    }
    
    .stat-card {
        @apply bg-white dark:bg-gray-800 rounded-lg shadow p-4 transition-all duration-300;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    
    /* Animated hamburger icon */
    .filter-icon {
        @apply h-5 w-5 text-gray-500 dark:text-gray-400;
        transition: transform 0.3s ease;
    }
    
    .filter-icon.active {
        transform: rotate(90deg);
    }
    
    /* Table enhancements */
    .enhanced-table {
        @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700 rounded-lg overflow-hidden;
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .enhanced-table thead {
        @apply bg-gray-100 dark:bg-gray-600;
    }
    
    .enhanced-table th {
        @apply sticky top-0 px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider bg-gray-100 dark:bg-gray-600;
        z-index: 10;
    }
    
    /* Tooltip styles */
    .tooltip {
        @apply relative inline-block;
    }
    
    .tooltip .tooltip-text {
        @apply absolute hidden z-10 p-2 text-xs text-white bg-gray-900 dark:bg-gray-700 rounded whitespace-nowrap;
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .tooltip:hover .tooltip-text {
        @apply block;
    }
    
    .tooltip .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #111827 transparent transparent transparent;
    }
    
    .dark .tooltip .tooltip-text::after {
        border-color: #374151 transparent transparent transparent;
    }
    
    /* Page title animation */
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(-10px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .animated-title {
        animation: slideIn 0.5s ease-out;
    }
    
    /* Action buttons hover effects */
    .action-button {
        @apply transition-all duration-200 transform;
    }
    
    .action-button:hover {
        @apply scale-110;
    }

    /* Customer avatar styles */
    .customer-avatar {
        @apply relative overflow-hidden;
        background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        box-shadow: 0 2px 10px rgba(99, 102, 241, 0.2);
        transition: all 0.3s ease;
        color: rgba(0, 0, 0, 0.7); /* Dark text for light mode */
    }

    .customer-avatar:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    .dark .customer-avatar {
        background: linear-gradient(135deg, #4f46e5 0%, #9333ea 100%);
        box-shadow: 0 2px 10px rgba(79, 70, 229, 0.3);
        color: rgba(255, 255, 255, 0.95); /* White text for dark mode */
    }

    .dark .customer-avatar:hover {
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
    }

    /* Enhanced avatar with colored variants based on first letter */
    .avatar-a, .avatar-j, .avatar-s { background: linear-gradient(135deg, #3b82f6, #2dd4bf); }
    .avatar-b, .avatar-k, .avatar-t { background: linear-gradient(135deg, #8b5cf6, #ec4899); }
    .avatar-c, .avatar-l, .avatar-u { background: linear-gradient(135deg, #f59e0b, #ef4444); }
    .avatar-d, .avatar-m, .avatar-v { background: linear-gradient(135deg, #10b981, #3b82f6); }
    .avatar-e, .avatar-n, .avatar-w { background: linear-gradient(135deg, #6366f1, #8b5cf6); }
    .avatar-f, .avatar-o, .avatar-x { background: linear-gradient(135deg, #f97316, #f59e0b); }
    .avatar-g, .avatar-p, .avatar-y { background: linear-gradient(135deg, #ec4899, #f97316); }
    .avatar-h, .avatar-q, .avatar-z { background: linear-gradient(135deg, #14b8a6, #6366f1); }
    .avatar-i, .avatar-r, .avatar-0 { background: linear-gradient(135deg, #ef4444, #f59e0b); }

    .dark .avatar-a, .dark .avatar-j, .dark .avatar-s { background: linear-gradient(135deg, #2563eb, #0d9488); }
    .dark .avatar-b, .dark .avatar-k, .dark .avatar-t { background: linear-gradient(135deg, #7c3aed, #db2777); }
    .dark .avatar-c, .dark .avatar-l, .dark .avatar-u { background: linear-gradient(135deg, #d97706, #dc2626); }
    .dark .avatar-d, .dark .avatar-m, .dark .avatar-v { background: linear-gradient(135deg, #059669, #2563eb); }
    .dark .avatar-e, .dark .avatar-n, .dark .avatar-w { background: linear-gradient(135deg, #4f46e5, #7c3aed); }
    .dark .avatar-f, .dark .avatar-o, .dark .avatar-x { background: linear-gradient(135deg, #ea580c, #d97706); }
    .dark .avatar-g, .dark .avatar-p, .dark .avatar-y { background: linear-gradient(135deg, #db2777, #ea580c); }
    .dark .avatar-h, .dark .avatar-q, .dark .avatar-z { background: linear-gradient(135deg, #0d9488, #4f46e5); }
    .dark .avatar-i, .dark .avatar-r, .dark .avatar-0 { background: linear-gradient(135deg, #dc2626, #d97706); }
</style>
@endsection

@section('content')
<div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6 text-gray-900 dark:text-gray-100">
        <!-- Header Section -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <h1 class="text-2xl font-bold animated-title flex items-center">
                <svg class="h-8 w-8 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                Repair Management
            </h1>
            <div class="flex flex-col md:flex-row items-center gap-2 md:gap-4 w-full md:w-auto">
                <!-- Enhanced Search Form -->
                <form id="searchForm" action="{{ route('repairs.index') }}" method="GET" class="flex items-center w-full md:w-auto relative">
                    <div class="relative flex-1 md:w-96">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-18 0 7 7 0 0118 0z"/>
                            </svg>
                        </div>
                        <input type="text" 
                               id="searchInput"
                               name="search" 
                               value="{{ request('search') }}" 
                               placeholder="Search by customer name, phone, device..." 
                               class="block w-full pl-10 pr-12 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white transition-all duration-200">
                        @if(request('search'))
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" id="clearSearch" class="text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                </form>
                
                <!-- New Repair Button -->
                <button id="openRepairModal" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 btn-hover-effect whitespace-nowrap">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    New Repair
                </button>
            </div>
        </div>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Pending Repairs -->
            <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 border border-yellow-200 dark:border-yellow-900">
                <div class="relative p-6">
                    <div class="absolute top-0 right-0 mt-4 mr-4 opacity-10">
                        <svg class="h-24 w-24 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="flex items-center justify-between relative">
                        <div>
                            <p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Pending Repairs</p>
                            <p class="text-3xl font-extrabold text-yellow-700 dark:text-yellow-300 mt-2 repair-stat-pending">{{ $repairs->where('status', 'pending')->count() }}</p>
                        </div>
                        <div class="p-3 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl shadow-lg">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4 border-t border-yellow-200 dark:border-yellow-900 pt-4">
                        <div class="text-xs text-yellow-600 dark:text-yellow-400 flex items-center">
                            <span class="flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Awaiting service or in progress
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Completed Repairs -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 border border-green-200 dark:border-green-900">
                <div class="relative p-6">
                    <div class="absolute top-0 right-0 mt-4 mr-4 opacity-10">
                        <svg class="h-24 w-24 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="flex items-center justify-between relative">
                        <div>
                            <p class="text-sm font-medium text-green-600 dark:text-green-400">Completed Repairs</p>
                            <p class="text-3xl font-extrabold text-green-700 dark:text-green-300 mt-2">{{ $repairs->where('status', 'completed')->count() }}</p>
                        </div>
                        <div class="p-3 bg-gradient-to-br from-green-400 to-green-500 rounded-xl shadow-lg">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4 border-t border-green-200 dark:border-green-900 pt-4">
                        <div class="text-xs text-green-600 dark:text-green-400 flex items-center">
                            <span class="flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Successfully finished repairs
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Total Revenue -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 border border-blue-200 dark:border-blue-900">
                <div class="relative p-6">
                    <div class="absolute top-0 right-0 mt-4 mr-4 opacity-10">
                        <svg class="h-24 w-24 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2"></path>
                        </svg>
                    </div>
                    <div class="flex items-center justify-between relative">
                        <div>
                            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Revenue</p>
                            <p class="text-3xl font-extrabold text-blue-700 dark:text-blue-300 mt-2">{{ number_format($repairs->where('status', 'completed')->sum('total_cost'), 2) }}</p>
                        </div>
                        <div class="p-3 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl shadow-lg">
                            <span class="h-8 w-8 text-white flex items-center justify-center text-2xl font-bold">₱</span>
                        </div>
                    </div>
                    <div class="mt-4 border-t border-blue-200 dark:border-blue-900 pt-4">
                        <div class="text-xs text-blue-600 dark:text-blue-400 flex items-center">
                            <span class="flex items-center">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                From completed repairs
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Total Repairs -->
            <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 border border-indigo-200 dark:border-indigo-900">
                <div class="relative p-6">
                    <div class="absolute top-0 right-0 mt-4 mr-4 opacity-10">
                        <svg class="h-24 w-24 text-indigo-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17 2H7a5 5 0 00-5 5v10a5 5 0 005 5h10a5 5 0 005-5V7a5 5 0 00-5-5z"></path>
                        </svg>
                    </div>
                    <div class="flex items-center justify-between relative">
                        <div>
                            <p class="text-sm font-medium text-indigo-600 dark:text-indigo-400">Total Repairs</p>
                            <p class="text-3xl font-extrabold text-indigo-700 dark:text-indigo-300 mt-2 repair-stat-total">{{ $repairs->count() }}</p>
                        </div>
                        <div class="p-3 bg-gradient-to-br from-indigo-400 to-indigo-500 rounded-xl shadow-lg">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="text-xs text-indigo-600 dark:text-indigo-400 flex items-center">
                        <span class="flex items-center">
                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            All time repair services
                        </span>
                    </div>
                </div>
            </div>
        </div>

        @if(session('success'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg flex items-center justify-between animate__animated animate__fadeIn">
                <div class="flex items-center">
                    <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>{{ session('success') }}</span>
                </div>
                <button type="button" class="text-green-700 hover:text-green-900 close-alert">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg flex items-center justify-between animate__animated animate__fadeIn">
                <div class="flex items-center">
                    <svg class="h-5 w-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <span>{{ session('error') }}</span>
                </div>
                <button type="button" class="text-red-700 hover:text-red-900 close-alert">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        @endif

        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden repairs-page">
        <div class="overflow-x-auto">
                <table class="enhanced-table">
                    <thead>
                    <tr>
                        @php
                            $headers = [
                                'customer' => 'Customer',
                                'device' => 'Device',
                                'service' => 'Service',
                                'status' => 'Status',
                                'created_at' => 'Date',
                                'actions' => 'Actions'
                            ];
                        @endphp

                        @foreach($headers as $key => $label)
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <a href="{{ route('repairs.index', [
                                    'sort' => $key,
                                    'direction' => ($sortField === $key && $sortDirection === 'asc') ? 'desc' : 'asc',
                                    'search' => request('search')
                                    ]) }}" class="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-300 group">
                                    <span>{{ $label }}</span>
                                        <span class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 {{ $sortField === $key ? 'opacity-100' : '' }}">
                                    @if($sortField === $key)
                                            @if($sortDirection === 'asc')
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                                    </svg>
                                                @else
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                    </svg>
                                                @endif
                                            @else
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                                </svg>
                                            @endif
                                        </span>
                                </a>
                            </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($repairs as $repair)
                            <tr class="table-row-hover">
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($repair->items->isNotEmpty())
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 rounded-full customer-avatar avatar-{{ strtolower(substr($repair->items->first()->device->customer->name, 0, 1)) }} flex items-center justify-center font-bold shadow-md">
                                                {{ strtoupper(substr($repair->items->first()->device->customer->name, 0, 1)) }}
                                            </div>
                                            <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <a href="{{ route('customers.index', ['highlight' => $repair->items->first()->device->customer->id]) }}" 
                                                       class="customer-profile-link text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 hover:underline"
                                                       onclick="event.stopPropagation();">
                                            {{ $repair->items->first()->device->customer->name }}
                                        </a>
                                    </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                                                    <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                    </svg>
                                        {{ $repair->items->first()->device->customer->phone }}
                                                </div>
                                            </div>
                                    </div>
                                @else
                                    <div class="text-sm text-gray-500 dark:text-gray-400">No items</div>
                                @endif
                            </td>
                                <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 dark:text-white">
                                        @foreach($repair->items as $index => $item)
                                            <div class="mb-1 flex items-center {{ $index > 0 ? 'mt-2 pt-2 border-t border-gray-100 dark:border-gray-700' : '' }}">
                                                <div class="flex-shrink-0 mr-2">
                                                    <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <span class="font-medium">
                                            @if($item->device->deviceModel)
                                                {{ $item->device->deviceModel->full_name }}
                                            @else
                                                {{ $item->device->brand }} {{ $item->device->model }}
                                            @endif
                                                    </span>
                                                    @if($item->device->serial_number)
                                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                            S/N: {{ $item->device->serial_number }}
                                                        </div>
                                                    @endif
                                                </div>
                                        </div>
                                    @endforeach
                                </div>
                            </td>
                                <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 dark:text-white">
                                        @foreach($repair->items as $index => $item)
                                            <div class="mb-1 flex items-center {{ $index > 0 ? 'mt-2 pt-2 border-t border-gray-100 dark:border-gray-700' : '' }}">
                                                <div class="flex-shrink-0 mr-2">
                                                    <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <span class="font-medium">{{ $item->service->name }}</span>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                ₱{{ number_format($item->cost, 2) }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-sm font-bold text-gray-900 dark:text-white mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                    Total: ₱{{ number_format($repair->total_cost, 2) }}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($repair->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                    @elseif($repair->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                    @elseif($repair->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                    @endif"
                    data-status="{{ $repair->status }}">
                    @if($repair->status === 'completed')
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                    @elseif($repair->status === 'pending')
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    @elseif($repair->status === 'cancelled')
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    @endif
                    {{ ucfirst($repair->status) }}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                    {{ $repair->created_at->format('M d, Y') }}
                </div>
                
                @if($repair->status === 'pending')
                    <div class="text-xs text-yellow-600 dark:text-yellow-400 mt-1 font-medium">
                        <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        Started on: {{ $repair->started_at ? Carbon\Carbon::parse($repair->started_at)->format('M d, Y') : $repair->created_at->format('M d, Y') }}
                    </div>
                @elseif($repair->status === 'completed')
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-medium">
                        <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Completed on: {{ $repair->completed_at ? Carbon\Carbon::parse($repair->completed_at)->format('M d, Y') : $repair->updated_at->format('M d, Y') }}
                    </div>
                    @if($repair->started_at && $repair->completed_at)
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Duration: {{ Carbon\Carbon::parse($repair->started_at)->diffForHumans(Carbon\Carbon::parse($repair->completed_at), true) }}
                        </div>
                    @endif
                @elseif($repair->status === 'cancelled')
                    <div class="text-xs text-red-600 dark:text-red-400 mt-1 font-medium">
                        <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Cancelled on: {{ $repair->updated_at->format('M d, Y') }}
                    </div>
                @endif
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-3">
                    <a href="{{ route('repairs.show', $repair) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 tooltip action-button">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                        <span class="tooltip-text">View</span>
                    </a>
                    
                    <a href="{{ route('repairs.edit', $repair) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 tooltip action-button">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        <span class="tooltip-text">Edit</span>
                    </a>
                    
                    <form action="{{ route('repairs.destroy', $repair) }}" method="POST" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this repair?');">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 tooltip action-button">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            <span class="tooltip-text">Delete</span>
                        </button>
                    </form>
                </div>
            </td>
        </tr>
    @empty
        <tr>
            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                No repairs found.
            </td>
        </tr>
    @endforelse
</tbody>
</table>
</div>

<div class="mt-4">
    {{ $repairs->links() }}
</div>
</div>
</div>
</div>

<!-- Repair Modal -->
<div id="repairModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity modal-overlay">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-800 opacity-75"></div>
        </div>
        
        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>
        
        <div id="modalContent" class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full opacity-0 translate-y-4 modal-content">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 modal-header">
                <div class="sm:flex sm:items-start">
                    <div class="w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">New Repair</h3>
                        
                        <!-- Form will be here -->
                        <form id="repairForm" action="{{ route('repairs.store') }}" method="POST" class="space-y-6">
                            @csrf

                            <!-- Customer Selection -->
                            <div class="border dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                                <h2 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">Customer Information</h2>
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select Customer</label>
                                    <select name="customer_id" id="customer_id" required
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Select a customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}">
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div id="repair-items">
                                <!-- Template for repair item -->
                                <template id="repair-item-template">
                                    <div class="repair-item border dark:border-gray-700 rounded-lg p-4 mb-4 bg-gray-50 dark:bg-gray-700">
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Device & Service</h3>
                                            <button type="button" onclick="removeRepairItem(this)" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                            </button>
                                        </div>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <!-- Device Selection -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Device</label>
                                                <select name="items[INDEX][device_id]" required
                                                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 device-select" disabled>
                                                    <option value="">Select a device</option>
                                                </select>
                                            </div>

                                            <!-- Service Selection -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Service</label>
                                                <select name="items[INDEX][service_id]" required
                                                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 service-select">
                                                    <option value="">Select a service</option>
                                                    @foreach($services as $service)
                                                        <option value="{{ $service->id }}" data-price="{{ $service->price }}">
                                                            {{ $service->name }} - ₱{{ number_format($service->price, 2) }} - {{ $service->category->name ?? 'Uncategorized' }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>

                                            <!-- Cost -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Cost</label>
                                                <div class="mt-1 relative rounded-md shadow-sm">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-500 dark:text-gray-400 sm:text-sm">₱</span>
                                                    </div>
                                                    <input type="number" name="items[INDEX][cost]" step="0.01" min="0" required
                                                        class="pl-7 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 cost-input">
                                                </div>
                                            </div>

                                            <!-- Notes -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                                                <textarea name="items[INDEX][notes]" rows="2"
                                                    class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <!-- Initial repair item -->
                                <div class="repair-item border dark:border-gray-700 rounded-lg p-4 mb-4 bg-gray-50 dark:bg-gray-700">
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Device & Service</h3>
                                        <button type="button" onclick="removeRepairItem(this)" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 hidden">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <!-- Device Selection -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Device</label>
                                            <select name="items[0][device_id]" required
                                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 device-select" disabled>
                                                <option value="">Select a device</option>
                                            </select>
                                        </div>

                                        <!-- Service Selection -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Service</label>
                                            <select name="items[0][service_id]" required
                                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 service-select">
                                                <option value="">Select a service</option>
                                                @foreach($services as $service)
                                                    <option value="{{ $service->id }}" data-price="{{ $service->price }}">
                                                        {{ $service->name }} - ₱{{ number_format($service->price, 2) }} - {{ $service->category->name ?? 'Uncategorized' }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <!-- Cost -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Cost</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 dark:text-gray-400 sm:text-sm">₱</span>
                                                </div>
                                                <input type="number" name="items[0][cost]" step="0.01" min="0" required
                                                    class="pl-7 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 cost-input">
                                            </div>
                                        </div>

                                        <!-- Notes -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                                            <textarea name="items[0][notes]" rows="2"
                                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Add Item Button -->
                            <div class="text-center">
                                <button type="button" id="add-item-btn" onclick="addRepairItem()" disabled
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 btn-hover-effect">
                                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                                    </svg>
                                    Add Another Device/Service
                                </button>
                            </div>
                            
                            <!-- Payment Information -->
                            <div class="border dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                                <h2 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">Payment Information</h2>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Status -->
                                    <div>
                                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                        <select name="status" id="status" required
                                            class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            <option value="pending">Pending</option>
                                            <option value="completed">Completed</option>
                                            <option value="cancelled">Cancelled</option>
                                        </select>
                                        
                                        <!-- Hidden date fields -->
                                        <input type="hidden" name="started_at" value="">
                                        <input type="hidden" name="completed_at" value="">
                                    </div>
                                    
                                    <!-- Payment Method -->
                                    <div>
                                        <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Method</label>
                                        <select name="payment_method" id="payment_method" required
                                            class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            <option value="cash">Cash</option>
                                            <option value="gcash">GCash</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                            <option value="credit_card">Credit Card</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Additional Notes -->
                            <div class="border dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700 mt-4">
                                <h2 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">Additional Notes</h2>
                                <div>
                                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                                    <textarea name="notes" id="notes" rows="3"
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse modal-footer">
                <button type="button" id="submitRepairForm" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800 dark:focus:ring-blue-600 sm:ml-3 sm:w-auto sm:text-sm btn-hover-effect">
                    Create Repair
                </button>
                <button type="button" id="closeModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-indigo-600 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm btn-hover-effect">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Run initialization when DOM is loaded or when page becomes visible
    document.addEventListener('DOMContentLoaded', initializeRepairsPage);
    
    // Handle client-side navigation via the custom page:loaded event
    document.addEventListener('page:loaded', initializeRepairsPage);
    
    // Also initialize when the page is shown (handles navigation without full page reload)
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            initializeRepairsPage();
        }
    });
    
    // Function to initialize everything on the repairs page
    function initializeRepairsPage() {
        console.log('Initializing repairs page');
        
        // Enhanced search functionality
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        const clearSearchBtn = document.getElementById('clearSearch');
        
        if (searchForm && searchInput) {
            // Debounce function
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
            
            // Handle search input
            searchInput.addEventListener('input', debounce(function(e) {
                if (e.target.value.length >= 2 || e.target.value.length === 0) {
                    searchForm.submit();
                }
            }, 500));
            
            // Clear search
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    searchInput.value = '';
                    searchForm.submit();
                });
            }
            
            // Prevent form submission on enter if input is empty or too short
            searchForm.addEventListener('submit', function(e) {
                if (searchInput.value.length === 1) {
                    e.preventDefault();
                }
            });
        }

        // Fix customer links to prevent event propagation issues
        document.querySelectorAll('.customer-profile-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.stopPropagation(); // Stop event from bubbling up
                // Allow default link behavior to occur (navigation)
            });
        });

        // Initialize repair modal and additional UI functionality
        initUI();
    }
    
    // Initialize modal functionality 
    function initUI() {
        console.log('Initializing repair modal');
        let itemIndex = 1;
        
        // Modal functionality
        const modal = document.getElementById('repairModal');
        const modalContent = document.getElementById('modalContent');
        const openModalBtn = document.getElementById('openRepairModal');
        const closeModalBtn = document.getElementById('closeModal');
        const submitFormBtn = document.getElementById('submitRepairForm');
        
        // Make sure all elements exist before adding event listeners
        if (modal && modalContent && openModalBtn && closeModalBtn && submitFormBtn) {
            // Remove any existing event listeners first to prevent duplicates
            openModalBtn.removeEventListener('click', window.openModal);
            closeModalBtn.removeEventListener('click', closeModal);
            submitFormBtn.removeEventListener('click', submitForm);
            
            // Add event listeners
            openModalBtn.addEventListener('click', window.openModal);
            closeModalBtn.addEventListener('click', closeModal);
            submitFormBtn.addEventListener('click', submitForm);
            
            // Close the modal when clicking outside of it
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        } else {
            console.error('Modal elements not found');
        }
        
        function submitForm() {
            const repairForm = document.getElementById('repairForm');
            if (!repairForm) return;
            
            // Show loading state on submit button
            const submitButton = document.getElementById('submitRepairForm');
            const originalButtonText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
            `;
            
            // Submit form via AJAX
            fetch(repairForm.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new FormData(repairForm)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const successAlert = document.createElement('div');
                    successAlert.className = 'fixed top-4 right-4 z-50 p-4 bg-green-100 border-l-4 border-green-500 rounded-md shadow-md fade-in';
                    successAlert.innerHTML = `
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">${data.message || 'Repair created successfully!'}</p>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(successAlert);
                    
                    // Update repairs table with new repair
                    updateRepairsTable(data.repair);
                    
                    // Close modal
                    closeModal();
                    
                    // Remove success message after 3 seconds
                    setTimeout(() => {
                        successAlert.style.opacity = '0';
                        successAlert.style.transform = 'translateY(-10px)';
                        successAlert.style.transition = 'all 0.3s ease-in-out';
                        setTimeout(() => successAlert.remove(), 300);
                    }, 3000);
                    
                    // Reset form
                    repairForm.reset();
                    
                    // Reset repair items (remove all except the first one)
                    const repairItems = document.querySelectorAll('.repair-item');
                    for (let i = 1; i < repairItems.length; i++) {
                        repairItems[i].remove();
                    }
                    itemIndex = 1;
                } else {
                    // Show error message
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'fixed top-4 right-4 z-50 p-4 bg-red-100 border-l-4 border-red-500 rounded-md shadow-md fade-in';
                    errorAlert.innerHTML = `
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-red-800">${data.message || 'Error creating repair'}</p>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(errorAlert);
                    
                    // Remove error message after 3 seconds
                    setTimeout(() => {
                        errorAlert.style.opacity = '0';
                        errorAlert.style.transform = 'translateY(-10px)';
                        errorAlert.style.transition = 'all 0.3s ease-in-out';
                        setTimeout(() => errorAlert.remove(), 300);
                    }, 3000);
                }
                
                // Restore button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            })
            .catch(error => {
                console.error('Error:', error);
                
                // Show error message
                const errorAlert = document.createElement('div');
                errorAlert.className = 'fixed top-4 right-4 z-50 p-4 bg-red-100 border-l-4 border-red-500 rounded-md shadow-md fade-in';
                errorAlert.innerHTML = `
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">An error occurred. Please try again.</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(errorAlert);
                
                // Remove error message after 3 seconds
                setTimeout(() => {
                    errorAlert.style.opacity = '0';
                    errorAlert.style.transform = 'translateY(-10px)';
                    errorAlert.style.transition = 'all 0.3s ease-in-out';
                    setTimeout(() => errorAlert.remove(), 300);
                }, 3000);
                
                // Restore button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
        }
        
        // Function to open modal with animation
        window.openModal = function() {
            console.log('Opening modal');
            // First make the modal visible but keep content transparent
            modal.classList.remove('hidden');
            
            // Add a small delay to ensure the transition works
            setTimeout(() => {
                // Make overlay visible
                const overlay = document.querySelector('.modal-overlay');
                if (overlay) overlay.classList.add('opacity-100');
                
                // Animate the modal from below
                modalContent.classList.remove('opacity-0', 'translate-y-4');
                modalContent.classList.add('opacity-100', 'translate-y-0');
            }, 10);
        }
        
        function closeModal() {
            console.log('Closing modal');
            // Animate the modal disappearing
            modalContent.classList.remove('opacity-100', 'translate-y-0');
            modalContent.classList.add('opacity-0', 'translate-y-4');
            
            // Fade out the overlay
            const overlay = document.querySelector('.modal-overlay');
            if (overlay) overlay.classList.remove('opacity-100');
            
            // After animation completes, hide the modal
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300); // Match this with your CSS transition duration
        }
        
        // Customer selection functionality
        const customerSelect = document.getElementById('customer_id');
        if (customerSelect) {
            customerSelect.addEventListener('change', function() {
                const customerId = this.value;
                document.querySelectorAll('.device-select').forEach(select => {
                    loadCustomerDevices(customerId, select);
                });
                const addItemBtn = document.getElementById('add-item-btn');
                if (addItemBtn) {
                    addItemBtn.disabled = !customerId;
                }
            });
        }
        
        // Service selection event delegation
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('service-select')) {
                const selectedOption = e.target.options[e.target.selectedIndex];
                if (selectedOption) {
                    const price = selectedOption.dataset.price;
                    const costInput = e.target.closest('.repair-item').querySelector('.cost-input');
                    if (price && costInput) {
                        costInput.value = price;
                    }
                }
            }
        });
    }
    
    // Function to load customer devices
    window.loadCustomerDevices = function(customerId, targetSelect) {
        if (!customerId) {
            targetSelect.innerHTML = '<option value="">Select a device</option>';
            targetSelect.disabled = true;
            return;
        }
        
        fetch(`/api/customers/${customerId}/devices`)
        .then(response => response.json())
        .then(data => {
            let options = '<option value="">Select a device</option>';
            data.forEach(device => {
                options += `<option value="${device.id}">${device.brand} ${device.model}</option>`;
            });
            targetSelect.innerHTML = options;
            targetSelect.disabled = false;
        })
        .catch(error => {
            console.error('Error:', error);
            targetSelect.innerHTML = '<option value="">Error loading devices</option>';
        });
    }
    
    // Function to add new repair item
    window.addRepairItem = function() {
        const template = document.getElementById('repair-item-template');
        if (!template) {
            console.error('Repair item template not found');
            return;
        }
        
        const clone = template.content.cloneNode(true);
        
        // Update the indices
        clone.querySelectorAll('[name*="[INDEX]"]').forEach(element => {
            element.name = element.name.replace('INDEX', itemIndex);
        });
        
        const repairItemsContainer = document.getElementById('repair-items');
        if (repairItemsContainer) {
            repairItemsContainer.appendChild(clone);
            
            // Load devices for the newly added select
            const customerId = document.getElementById('customer_id')?.value;
            if (customerId) {
                const newDeviceSelect = document.querySelectorAll('.device-select')[itemIndex];
                if (newDeviceSelect) {
                    loadCustomerDevices(customerId, newDeviceSelect);
                }
            }
            
            itemIndex++;
        }
    }
    
    // Function to remove repair item
    window.removeRepairItem = function(button) {
        if (button) {
            const repairItem = button.closest('.repair-item');
            if (repairItem) {
                repairItem.remove();
            }
        }
    }

    // Function to update the repairs table with a newly created repair
    function updateRepairsTable(repair) {
        // Get the repairs table body
        const repairsTableBody = document.querySelector('table tbody');
        if (!repairsTableBody) return;
        
        // Create a new row for the repair
        const newRow = document.createElement('tr');
        newRow.className = 'table-row-hover fade-in';
        
        // Format the date
        const createdDate = new Date(repair.created_at);
        const formattedDate = createdDate.toLocaleDateString('en-US', {
            year: 'numeric', 
            month: 'short', 
            day: 'numeric'
        });
        
        // Get the first repair item
        const firstItem = repair.items[0];
        const customer = firstItem.device.customer;
        const device = firstItem.device;
        const service = firstItem.service;
        const status = firstItem.status;
        
        // Get status class based on repair status
        let statusClass = 'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300';
        let statusIcon = '';
        
        if (status) {
            switch(status.toLowerCase()) {
                case 'completed':
                    statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
                    statusIcon = '<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>';
                    break;
                case 'pending':
                    statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
                    statusIcon = '<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>';
                    break;
                case 'cancelled':
                    statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
                    statusIcon = '<svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/></svg>';
                    break;
            }
        }
        
        // Generate avatar color class based on customer name
        const avatarClass = `avatar-${customer.name.charAt(0).toLowerCase()}`;
        
        // Create the row HTML
        newRow.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full customer-avatar ${avatarClass} flex items-center justify-center font-bold shadow-md">
                        ${customer.name.charAt(0).toUpperCase()}
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                            <a href="/customers?highlight=${customer.id}" 
                               class="customer-profile-link text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 hover:underline"
                               onclick="event.stopPropagation();">
                                ${customer.name}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            ${customer.phone}
                        </div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-white">
                    <div class="mb-1 flex items-center">
                        <div class="flex-shrink-0 mr-2">
                            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <span class="font-medium">
                                ${device.brand} ${device.model}
                            </span>
                            ${device.serial_number ? 
                                `<div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    S/N: ${device.serial_number}
                                </div>` : ''}
                        </div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-white">
                    <div class="mb-1 flex items-center">
                        <div class="flex-shrink-0 mr-2">
                            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <span class="font-medium">${service.name}</span>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                ₱${parseFloat(service.price).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-sm font-bold text-gray-900 dark:text-white mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                    Total: ₱${repair.items.reduce((total, item) => total + parseFloat(item.cost), 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusClass}" data-status="${status}">
                    ${statusIcon}
                    ${status.charAt(0).toUpperCase() + status.slice(1)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">
                    ${formattedDate}
                </div>
                
                ${status === 'pending' ? 
                    `<div class="text-xs text-yellow-600 dark:text-yellow-400 mt-1 font-medium">
                        <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        Started on: ${formattedDate}
                    </div>` 
                : status === 'completed' ?
                    `<div class="text-xs text-green-600 dark:text-green-400 mt-1 font-medium">
                        <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Completed on: ${formattedDate}
                    </div>`
                : status === 'cancelled' ?
                    `<div class="text-xs text-red-600 dark:text-red-400 mt-1 font-medium">
                        <svg class="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Cancelled on: ${formattedDate}
                    </div>`
                : ''
                }
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                    <a href="/repairs/${repair.id}/edit" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" title="Edit Repair">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </a>
                    <form action="/repairs/${repair.id}" method="POST" class="inline-block">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="Delete Repair" onclick="return confirm('Are you sure you want to delete this repair?');">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </td>
        `;
        
        // Add the new row to the beginning of the table
        if (repairsTableBody.firstChild) {
            repairsTableBody.insertBefore(newRow, repairsTableBody.firstChild);
        } else {
            repairsTableBody.appendChild(newRow);
        }
        
        // Highlight the new row
        setTimeout(() => {
            newRow.classList.add('bg-green-50', 'dark:bg-green-900/20');
            setTimeout(() => {
                newRow.classList.remove('bg-green-50', 'dark:bg-green-900/20');
                newRow.classList.add('transition-colors', 'duration-1000');
            }, 2000);
        }, 300);
        
        // Update repair stats if they exist
        updateRepairStats();
    }
    
    // Function to update repair stats on the repairs page
    function updateRepairStats() {
        const pendingCount = document.querySelector('.repair-stat-pending');
        const completedCount = document.querySelector('.repair-stat-completed');
        const totalCount = document.querySelector('.repair-stat-total');
        
        if (pendingCount) {
            const currentCount = parseInt(pendingCount.textContent.trim());
            if (!isNaN(currentCount)) {
                pendingCount.textContent = (currentCount + 1).toString();
            }
        }
        
        if (totalCount) {
            const currentCount = parseInt(totalCount.textContent.trim());
            if (!isNaN(currentCount)) {
                totalCount.textContent = (currentCount + 1).toString();
            }
        }
    }
    
    // Call initUI to initialize the modal functionality
    initUI();
} // End of initializeRepairsPage function
</script>
@endpush
@endsection 